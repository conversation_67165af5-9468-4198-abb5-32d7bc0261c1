"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/edit/[id]/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/edit/[id]/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/[locale]/edit/[id]/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MapEditPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _connect__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/connect */ \"(app-pages-browser)/./src/connect.ts\");\n/* harmony import */ var _utils_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/toast */ \"(app-pages-browser)/./src/utils/toast.ts\");\n/* harmony import */ var _mind_elixir_node_menu_neo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mind-elixir/node-menu-neo */ \"(app-pages-browser)/./node_modules/.pnpm/@mind-elixir+node-menu-neo@1.0.4/node_modules/@mind-elixir/node-menu-neo/dist/node-menu-neo.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// @ts-ignore\n\nconst MindElixirReactComponent = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_MindElixirReact_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/MindElixirReact */ \"(app-pages-browser)/./src/components/MindElixirReact.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx -> \" + \"@/components/MindElixirReact\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex items-center justify-center\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n            lineNumber: 19,\n            columnNumber: 20\n        }, undefined)\n});\n_c = MindElixirReactComponent;\n// Create a wrapper component that properly forwards refs\nconst MindElixirReact = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MindElixirReactComponent, {\n        ...props,\n        ref: ref\n    }, void 0, false, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n        lineNumber: 25,\n        columnNumber: 10\n    }, undefined);\n});\n_c1 = MindElixirReact;\nfunction MapEditPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"button\");\n    const [mapData, setMapData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [isUnsaved, setIsUnsaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSavedTime, setLastSavedTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const meRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const plugins = [\n        _mind_elixir_node_menu_neo__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n    const options = {\n        el: \"\",\n        direction: 2,\n        allowUndo: true\n    };\n    const mapId = params.id;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchMap = async ()=>{\n            try {\n                const res = await _connect__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/api/map/\".concat(mapId));\n                setMapData(res.data.content);\n            } catch (error) {\n                console.error(\"Failed to fetch map:\", error);\n                router.push(\"/404\");\n            }\n        };\n        if (mapId) {\n            fetchMap();\n        }\n    }, [\n        mapId,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _meRef_current;\n        const instance = (_meRef_current = meRef.current) === null || _meRef_current === void 0 ? void 0 : _meRef_current.instance;\n        if (instance) {\n            var _instance_map;\n            const handleOperation = ()=>{\n                setIsUnsaved(true);\n            };\n            const handleKeydown = (e)=>{\n                e.preventDefault();\n                if (e.target !== e.currentTarget) {\n                    return;\n                }\n                if (e.ctrlKey && e.key === \"s\") {\n                    save();\n                }\n            };\n            instance.bus.addListener(\"operation\", handleOperation);\n            (_instance_map = instance.map) === null || _instance_map === void 0 ? void 0 : _instance_map.addEventListener(\"keydown\", handleKeydown);\n            return ()=>{\n                var _instance_map;\n                instance.bus.removeListener(\"operation\", handleOperation);\n                (_instance_map = instance.map) === null || _instance_map === void 0 ? void 0 : _instance_map.removeEventListener(\"keydown\", handleKeydown);\n            };\n        }\n    }, [\n        mapData\n    ]);\n    const save = async ()=>{\n        var _meRef_current;\n        if (saving || !isUnsaved || !((_meRef_current = meRef.current) === null || _meRef_current === void 0 ? void 0 : _meRef_current.instance)) return;\n        setSaving(true);\n        try {\n            const newData = meRef.current.instance.getData();\n            newData.theme = undefined;\n            await _connect__WEBPACK_IMPORTED_MODULE_4__[\"default\"].patch(\"/api/map/\".concat(mapId), {\n                name: newData.nodeData.topic,\n                content: newData\n            });\n            setSaving(false);\n            setIsUnsaved(false);\n            setLastSavedTime(new Date().toLocaleString());\n            _utils_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"Saved\");\n        } catch (error) {\n            setSaving(false);\n            _utils_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Failed to save\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleBeforeUnload = (e)=>{\n            if (isUnsaved) {\n                e.preventDefault();\n                e.returnValue = \"\";\n            }\n        };\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return ()=>window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n    }, [\n        isUnsaved\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MindElixirReact, {\n                ref: meRef,\n                data: mapData,\n                plugins: plugins,\n                options: options,\n                className: \"h-screen\"\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            isUnsaved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-10 left-6 dark:text-gray-200\",\n                children: \"Unsaved\"\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, this),\n            lastSavedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-6 left-6 dark:text-gray-200\",\n                children: [\n                    \"Last saved time: \",\n                    lastSavedTime\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-5 right-8 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"btn\",\n                    onClick: save,\n                    disabled: saving || !isUnsaved,\n                    children: [\n                        saving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"loading loading-spinner\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 22\n                        }, this),\n                        t(\"save\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(MapEditPage, \"6DWkkKqkXBfu1U67WzK9u4fqsjk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations\n    ];\n});\n_c2 = MapEditPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"MindElixirReactComponent\");\n$RefreshReg$(_c1, \"MindElixirReact\");\n$RefreshReg$(_c2, \"MapEditPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/edit/[id]/page.tsx\n"));

/***/ })

});