"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/edit/[id]/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/edit/[id]/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/[locale]/edit/[id]/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MapEditPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _connect__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/connect */ \"(app-pages-browser)/./src/connect.ts\");\n/* harmony import */ var _utils_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/toast */ \"(app-pages-browser)/./src/utils/toast.ts\");\n/* harmony import */ var _mind_elixir_node_menu_neo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mind-elixir/node-menu-neo */ \"(app-pages-browser)/./node_modules/.pnpm/@mind-elixir+node-menu-neo@1.0.4/node_modules/@mind-elixir/node-menu-neo/dist/node-menu-neo.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// @ts-ignore\n\nconst MindElixirReact = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_MindElixirReact_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/MindElixirReact */ \"(app-pages-browser)/./src/components/MindElixirReact.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx -> \" + \"@/components/MindElixirReact\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex items-center justify-center\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n            lineNumber: 17,\n            columnNumber: 18\n        }, undefined)\n});\n_c = MindElixirReact;\nfunction MapEditPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"button\");\n    const [mapData, setMapData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [isUnsaved, setIsUnsaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSavedTime, setLastSavedTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const meRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const plugins = [\n        _mind_elixir_node_menu_neo__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n    const options = {\n        el: \"\",\n        direction: 2,\n        allowUndo: true\n    };\n    const mapId = params.id;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchMap = async ()=>{\n            try {\n                const res = await _connect__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/api/map/\".concat(mapId));\n                setMapData(res.data.content);\n            } catch (error) {\n                console.error(\"Failed to fetch map:\", error);\n                router.push(\"/404\");\n            }\n        };\n        if (mapId) {\n            fetchMap();\n        }\n    }, [\n        mapId,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _meRef_current;\n        const instance = (_meRef_current = meRef.current) === null || _meRef_current === void 0 ? void 0 : _meRef_current.instance;\n        if (instance) {\n            var _instance_map;\n            const handleOperation = ()=>{\n                setIsUnsaved(true);\n            };\n            const handleKeydown = (e)=>{\n                e.preventDefault();\n                if (e.target !== e.currentTarget) {\n                    return;\n                }\n                if (e.ctrlKey && e.key === \"s\") {\n                    save();\n                }\n            };\n            instance.bus.addListener(\"operation\", handleOperation);\n            (_instance_map = instance.map) === null || _instance_map === void 0 ? void 0 : _instance_map.addEventListener(\"keydown\", handleKeydown);\n            return ()=>{\n                var _instance_map;\n                instance.bus.removeListener(\"operation\", handleOperation);\n                (_instance_map = instance.map) === null || _instance_map === void 0 ? void 0 : _instance_map.removeEventListener(\"keydown\", handleKeydown);\n            };\n        }\n    }, [\n        mapData\n    ]);\n    const save = async ()=>{\n        var _meRef_current;\n        if (saving || !isUnsaved || !((_meRef_current = meRef.current) === null || _meRef_current === void 0 ? void 0 : _meRef_current.instance)) return;\n        setSaving(true);\n        try {\n            const newData = meRef.current.instance.getData();\n            newData.theme = undefined;\n            await _connect__WEBPACK_IMPORTED_MODULE_4__[\"default\"].patch(\"/api/map/\".concat(mapId), {\n                name: newData.nodeData.topic,\n                content: newData\n            });\n            setSaving(false);\n            setIsUnsaved(false);\n            setLastSavedTime(new Date().toLocaleString());\n            _utils_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"Saved\");\n        } catch (error) {\n            setSaving(false);\n            _utils_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Failed to save\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleBeforeUnload = (e)=>{\n            if (isUnsaved) {\n                e.preventDefault();\n                e.returnValue = \"\";\n            }\n        };\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return ()=>window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n    }, [\n        isUnsaved\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MindElixirReact, {\n                ref: meRef,\n                data: mapData,\n                plugins: plugins,\n                options: options,\n                className: \"h-screen\"\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            isUnsaved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-10 left-6 dark:text-gray-200\",\n                children: \"Unsaved\"\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this),\n            lastSavedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-6 left-6 dark:text-gray-200\",\n                children: [\n                    \"Last saved time: \",\n                    lastSavedTime\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-5 right-8 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"btn\",\n                    onClick: save,\n                    disabled: saving || !isUnsaved,\n                    children: [\n                        saving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"loading loading-spinner\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 22\n                        }, this),\n                        t(\"save\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(MapEditPage, \"6DWkkKqkXBfu1U67WzK9u4fqsjk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations\n    ];\n});\n_c1 = MapEditPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"MindElixirReact\");\n$RefreshReg$(_c1, \"MapEditPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/edit/[id]/page.tsx\n"));

/***/ })

});