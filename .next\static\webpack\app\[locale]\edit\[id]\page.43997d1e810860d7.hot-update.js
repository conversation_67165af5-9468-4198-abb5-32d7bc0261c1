"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/edit/[id]/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/edit/[id]/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/[locale]/edit/[id]/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MapEditPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _connect__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/connect */ \"(app-pages-browser)/./src/connect.ts\");\n/* harmony import */ var _utils_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/toast */ \"(app-pages-browser)/./src/utils/toast.ts\");\n/* harmony import */ var _mind_elixir_node_menu_neo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mind-elixir/node-menu-neo */ \"(app-pages-browser)/./node_modules/.pnpm/@mind-elixir+node-menu-neo@1.0.4/node_modules/@mind-elixir/node-menu-neo/dist/node-menu-neo.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// @ts-ignore\n\n// 动态导入组件\nconst DynamicMindElixirReact = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_MindElixirReact_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/MindElixirReact */ \"(app-pages-browser)/./src/components/MindElixirReact.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx -> \" + \"@/components/MindElixirReact\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex items-center justify-center\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n            lineNumber: 18,\n            columnNumber: 18\n        }, undefined)\n});\n_c = DynamicMindElixirReact;\n// 创建一个包装组件来正确处理 ref 转发\nconst MindElixirReact = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, ref)=>{\n    // @ts-ignore - 忽略动态组件的 ref 类型检查\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DynamicMindElixirReact, {\n        ...props,\n        ref: ref\n    }, void 0, false, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n        lineNumber: 24,\n        columnNumber: 10\n    }, undefined);\n});\n_c1 = MindElixirReact;\nMindElixirReact.displayName = \"MindElixirReact\";\nfunction MapEditPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"button\");\n    const [mapData, setMapData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [isUnsaved, setIsUnsaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSavedTime, setLastSavedTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [mindElixirInstance, setMindElixirInstance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const plugins = [\n        _mind_elixir_node_menu_neo__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n    const options = {\n        el: \"\",\n        direction: 2,\n        allowUndo: true\n    };\n    const mapId = params.id;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchMap = async ()=>{\n            try {\n                const res = await _connect__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/api/map/\".concat(mapId));\n                setMapData(res.data.content);\n            } catch (error) {\n                console.error(\"Failed to fetch map:\", error);\n                router.push(\"/404\");\n            }\n        };\n        if (mapId) {\n            fetchMap();\n        }\n    }, [\n        mapId,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const instance = mindElixirInstance === null || mindElixirInstance === void 0 ? void 0 : mindElixirInstance.instance;\n        if (instance) {\n            var _instance_map;\n            const handleOperation = ()=>{\n                setIsUnsaved(true);\n            };\n            const handleKeydown = (e)=>{\n                e.preventDefault();\n                if (e.target !== e.currentTarget) {\n                    return;\n                }\n                if (e.ctrlKey && e.key === \"s\") {\n                    save();\n                }\n            };\n            instance.bus.addListener(\"operation\", handleOperation);\n            (_instance_map = instance.map) === null || _instance_map === void 0 ? void 0 : _instance_map.addEventListener(\"keydown\", handleKeydown);\n            return ()=>{\n                var _instance_map;\n                instance.bus.removeListener(\"operation\", handleOperation);\n                (_instance_map = instance.map) === null || _instance_map === void 0 ? void 0 : _instance_map.removeEventListener(\"keydown\", handleKeydown);\n            };\n        }\n    }, [\n        mapData,\n        mindElixirInstance\n    ]);\n    const save = async ()=>{\n        if (saving || !isUnsaved || !(mindElixirInstance === null || mindElixirInstance === void 0 ? void 0 : mindElixirInstance.instance)) return;\n        setSaving(true);\n        try {\n            const newData = mindElixirInstance.instance.getData();\n            newData.theme = undefined;\n            await _connect__WEBPACK_IMPORTED_MODULE_4__[\"default\"].patch(\"/api/map/\".concat(mapId), {\n                name: newData.nodeData.topic,\n                content: newData\n            });\n            setSaving(false);\n            setIsUnsaved(false);\n            setLastSavedTime(new Date().toLocaleString());\n            _utils_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"Saved\");\n        } catch (error) {\n            setSaving(false);\n            _utils_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Failed to save\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleBeforeUnload = (e)=>{\n            if (isUnsaved) {\n                e.preventDefault();\n                e.returnValue = \"\";\n            }\n        };\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return ()=>window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n    }, [\n        isUnsaved\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MindElixirReact, {\n                ref: meRef,\n                data: mapData,\n                plugins: plugins,\n                options: options,\n                className: \"h-screen\"\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            isUnsaved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-10 left-6 dark:text-gray-200\",\n                children: \"Unsaved\"\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, this),\n            lastSavedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-6 left-6 dark:text-gray-200\",\n                children: [\n                    \"Last saved time: \",\n                    lastSavedTime\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-5 right-8 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"btn\",\n                    onClick: save,\n                    disabled: saving || !isUnsaved,\n                    children: [\n                        saving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"loading loading-spinner\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 22\n                        }, this),\n                        t(\"save\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(MapEditPage, \"MDO1zNWaq+bffByBnOg6dX4mvoo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations\n    ];\n});\n_c2 = MapEditPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"DynamicMindElixirReact\");\n$RefreshReg$(_c1, \"MindElixirReact\");\n$RefreshReg$(_c2, \"MapEditPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/edit/[id]/page.tsx\n"));

/***/ })

});