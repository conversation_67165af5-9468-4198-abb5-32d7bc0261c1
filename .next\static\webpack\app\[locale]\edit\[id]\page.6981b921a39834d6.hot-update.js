"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/edit/[id]/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/edit/[id]/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/[locale]/edit/[id]/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MapEditPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _connect__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/connect */ \"(app-pages-browser)/./src/connect.ts\");\n/* harmony import */ var _utils_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/toast */ \"(app-pages-browser)/./src/utils/toast.ts\");\n/* harmony import */ var _mind_elixir_node_menu_neo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mind-elixir/node-menu-neo */ \"(app-pages-browser)/./node_modules/.pnpm/@mind-elixir+node-menu-neo@1.0.4/node_modules/@mind-elixir/node-menu-neo/dist/node-menu-neo.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// @ts-ignore\n\nconst MindElixirReact = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_MindElixirReact_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/MindElixirReact */ \"(app-pages-browser)/./src/components/MindElixirReact.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx -> \" + \"@/components/MindElixirReact\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex items-center justify-center\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n            lineNumber: 16,\n            columnNumber: 18\n        }, undefined)\n});\n_c = MindElixirReact;\nfunction MapEditPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"button\");\n    const [mapData, setMapData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [isUnsaved, setIsUnsaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSavedTime, setLastSavedTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const meRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const plugins = [\n        _mind_elixir_node_menu_neo__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n    const options = {\n        el: \"\",\n        direction: 2,\n        allowUndo: true\n    };\n    const mapId = params.id;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchMap = async ()=>{\n            try {\n                const res = await _connect__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/api/map/\".concat(mapId));\n                setMapData(res.data.content);\n            } catch (error) {\n                console.error(\"Failed to fetch map:\", error);\n                router.push(\"/404\");\n            }\n        };\n        if (mapId) {\n            fetchMap();\n        }\n    }, [\n        mapId,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _meRef_current;\n        const instance = (_meRef_current = meRef.current) === null || _meRef_current === void 0 ? void 0 : _meRef_current.instance;\n        if (instance) {\n            var _instance_map;\n            const handleOperation = ()=>{\n                setIsUnsaved(true);\n            };\n            const handleKeydown = (e)=>{\n                e.preventDefault();\n                if (e.target !== e.currentTarget) {\n                    return;\n                }\n                if (e.ctrlKey && e.key === \"s\") {\n                    save();\n                }\n            };\n            instance.bus.addListener(\"operation\", handleOperation);\n            (_instance_map = instance.map) === null || _instance_map === void 0 ? void 0 : _instance_map.addEventListener(\"keydown\", handleKeydown);\n            return ()=>{\n                var _instance_map;\n                instance.bus.removeListener(\"operation\", handleOperation);\n                (_instance_map = instance.map) === null || _instance_map === void 0 ? void 0 : _instance_map.removeEventListener(\"keydown\", handleKeydown);\n            };\n        }\n    }, [\n        mapData\n    ]);\n    const save = async ()=>{\n        var _meRef_current;\n        if (saving || !isUnsaved || !((_meRef_current = meRef.current) === null || _meRef_current === void 0 ? void 0 : _meRef_current.instance)) return;\n        setSaving(true);\n        try {\n            const newData = meRef.current.instance.getData();\n            newData.theme = undefined;\n            await _connect__WEBPACK_IMPORTED_MODULE_4__[\"default\"].patch(\"/api/map/\".concat(mapId), {\n                name: newData.nodeData.topic,\n                content: newData\n            });\n            setSaving(false);\n            setIsUnsaved(false);\n            setLastSavedTime(new Date().toLocaleString());\n            _utils_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"Saved\");\n        } catch (error) {\n            setSaving(false);\n            _utils_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Failed to save\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleBeforeUnload = (e)=>{\n            if (isUnsaved) {\n                e.preventDefault();\n                e.returnValue = \"\";\n            }\n        };\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return ()=>window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n    }, [\n        isUnsaved\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MindElixirReact, {\n                ref: meRef,\n                data: mapData,\n                plugins: plugins,\n                options: options,\n                className: \"h-screen\"\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            isUnsaved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-10 left-6 dark:text-gray-200\",\n                children: \"Unsaved\"\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, this),\n            lastSavedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-6 left-6 dark:text-gray-200\",\n                children: [\n                    \"Last saved time: \",\n                    lastSavedTime\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-5 right-8 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"btn\",\n                    onClick: save,\n                    disabled: saving || !isUnsaved,\n                    children: [\n                        saving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"loading loading-spinner\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 22\n                        }, this),\n                        t(\"save\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(MapEditPage, \"6DWkkKqkXBfu1U67WzK9u4fqsjk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations\n    ];\n});\n_c1 = MapEditPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"MindElixirReact\");\n$RefreshReg$(_c1, \"MapEditPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/edit/[id]/page.tsx\n"));

/***/ })

});