"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/page.tsx":
/*!***********************************!*\
  !*** ./src/app/[locale]/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var mind_elixir_example__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mind-elixir/example */ \"(app-pages-browser)/./node_modules/.pnpm/mind-elixir@5.0.0-beta.21/node_modules/mind-elixir/dist/example.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst MindElixirReact = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_MindElixirReact_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/MindElixirReact */ \"(app-pages-browser)/./src/components/MindElixirReact.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\[locale]\\\\page.tsx -> \" + \"@/components/MindElixirReact\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex items-center justify-center\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n            lineNumber: 9,\n            columnNumber: 5\n        }, undefined)\n});\n_c = MindElixirReact;\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MindElixirReact, {\n            className: \"h-full\",\n            data: mind_elixir_example__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            options: {\n                toolBar: true\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n_c1 = HomePage;\nvar _c, _c1;\n$RefreshReg$(_c, \"MindElixirReact\");\n$RefreshReg$(_c1, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvW2xvY2FsZV0vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFa0M7QUFDSTtBQUV0QyxNQUFNRSxrQkFBa0JGLHdEQUFPQSxDQUFDLElBQU0sa1BBQU87Ozs7OztJQUMzQ0csS0FBSztJQUNMQyxTQUFTLGtCQUNQLDhEQUFDQztZQUFJQyxXQUFVO3NCQUE0Qzs7Ozs7OztLQUh6REo7QUFPUyxTQUFTSztJQUN0QixxQkFDRSw4REFBQ0Y7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0o7WUFDQ0ksV0FBVTtZQUNWTCxNQUFNQSwyREFBSUE7WUFDVk8sU0FBUztnQkFDUEMsU0FBUztZQUNYOzs7Ozs7Ozs7OztBQUlSO01BWndCRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL1tsb2NhbGVdL3BhZ2UudHN4P2E0N2IiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBkeW5hbWljIGZyb20gJ25leHQvZHluYW1pYydcbmltcG9ydCBkYXRhIGZyb20gJ21pbmQtZWxpeGlyL2V4YW1wbGUnXG5cbmNvbnN0IE1pbmRFbGl4aXJSZWFjdCA9IGR5bmFtaWMoKCkgPT4gaW1wb3J0KCdAL2NvbXBvbmVudHMvTWluZEVsaXhpclJlYWN0JyksIHtcbiAgc3NyOiBmYWxzZSxcbiAgbG9hZGluZzogKCkgPT4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5Mb2FkaW5nLi4uPC9kaXY+XG4gICksXG59KVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lUGFnZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImgtc2NyZWVuXCI+XG4gICAgICA8TWluZEVsaXhpclJlYWN0XG4gICAgICAgIGNsYXNzTmFtZT0naC1mdWxsJ1xuICAgICAgICBkYXRhPXtkYXRhfVxuICAgICAgICBvcHRpb25zPXt7XG4gICAgICAgICAgdG9vbEJhcjogdHJ1ZSxcbiAgICAgICAgfX1cbiAgICAgIC8+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJkeW5hbWljIiwiZGF0YSIsIk1pbmRFbGl4aXJSZWFjdCIsInNzciIsImxvYWRpbmciLCJkaXYiLCJjbGFzc05hbWUiLCJIb21lUGFnZSIsIm9wdGlvbnMiLCJ0b29sQmFyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/page.tsx\n"));

/***/ })

});