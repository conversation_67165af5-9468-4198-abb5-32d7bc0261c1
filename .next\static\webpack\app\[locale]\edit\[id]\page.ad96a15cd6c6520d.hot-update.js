"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/edit/[id]/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/edit/[id]/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/[locale]/edit/[id]/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MapEditPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _connect__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/connect */ \"(app-pages-browser)/./src/connect.ts\");\n/* harmony import */ var _utils_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/toast */ \"(app-pages-browser)/./src/utils/toast.ts\");\n/* harmony import */ var _mind_elixir_node_menu_neo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mind-elixir/node-menu-neo */ \"(app-pages-browser)/./node_modules/.pnpm/@mind-elixir+node-menu-neo@1.0.4/node_modules/@mind-elixir/node-menu-neo/dist/node-menu-neo.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// @ts-ignore\n\nconst MindElixirReact = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_MindElixirReact_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/MindElixirReact */ \"(app-pages-browser)/./src/components/MindElixirReact.tsx\")).then((mod)=>({\n            default: mod.default\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx -> \" + \"@/components/MindElixirReact\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex items-center justify-center\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n            lineNumber: 19,\n            columnNumber: 20\n        }, undefined)\n});\n_c = MindElixirReact;\nfunction MapEditPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"button\");\n    const [mapData, setMapData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [isUnsaved, setIsUnsaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSavedTime, setLastSavedTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const meRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const plugins = [\n        _mind_elixir_node_menu_neo__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n    const options = {\n        el: \"\",\n        direction: 2,\n        allowUndo: true\n    };\n    const mapId = params.id;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchMap = async ()=>{\n            try {\n                const res = await _connect__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/api/map/\".concat(mapId));\n                setMapData(res.data.content);\n            } catch (error) {\n                console.error(\"Failed to fetch map:\", error);\n                router.push(\"/404\");\n            }\n        };\n        if (mapId) {\n            fetchMap();\n        }\n    }, [\n        mapId,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _meRef_current;\n        const instance = (_meRef_current = meRef.current) === null || _meRef_current === void 0 ? void 0 : _meRef_current.instance;\n        if (instance) {\n            var _instance_map;\n            const handleOperation = ()=>{\n                setIsUnsaved(true);\n            };\n            const handleKeydown = (e)=>{\n                e.preventDefault();\n                if (e.target !== e.currentTarget) {\n                    return;\n                }\n                if (e.ctrlKey && e.key === \"s\") {\n                    save();\n                }\n            };\n            instance.bus.addListener(\"operation\", handleOperation);\n            (_instance_map = instance.map) === null || _instance_map === void 0 ? void 0 : _instance_map.addEventListener(\"keydown\", handleKeydown);\n            return ()=>{\n                var _instance_map;\n                instance.bus.removeListener(\"operation\", handleOperation);\n                (_instance_map = instance.map) === null || _instance_map === void 0 ? void 0 : _instance_map.removeEventListener(\"keydown\", handleKeydown);\n            };\n        }\n    }, [\n        mapData\n    ]);\n    const save = async ()=>{\n        var _meRef_current;\n        if (saving || !isUnsaved || !((_meRef_current = meRef.current) === null || _meRef_current === void 0 ? void 0 : _meRef_current.instance)) return;\n        setSaving(true);\n        try {\n            const newData = meRef.current.instance.getData();\n            newData.theme = undefined;\n            await _connect__WEBPACK_IMPORTED_MODULE_4__[\"default\"].patch(\"/api/map/\".concat(mapId), {\n                name: newData.nodeData.topic,\n                content: newData\n            });\n            setSaving(false);\n            setIsUnsaved(false);\n            setLastSavedTime(new Date().toLocaleString());\n            _utils_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"Saved\");\n        } catch (error) {\n            setSaving(false);\n            _utils_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Failed to save\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleBeforeUnload = (e)=>{\n            if (isUnsaved) {\n                e.preventDefault();\n                e.returnValue = \"\";\n            }\n        };\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return ()=>window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n    }, [\n        isUnsaved\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MindElixirReact, {\n                ref: meRef,\n                data: mapData,\n                plugins: plugins,\n                options: options,\n                className: \"h-screen\"\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            isUnsaved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-10 left-6 dark:text-gray-200\",\n                children: \"Unsaved\"\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, this),\n            lastSavedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-6 left-6 dark:text-gray-200\",\n                children: [\n                    \"Last saved time: \",\n                    lastSavedTime\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-5 right-8 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"btn\",\n                    onClick: save,\n                    disabled: saving || !isUnsaved,\n                    children: [\n                        saving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"loading loading-spinner\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 22\n                        }, this),\n                        t(\"save\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(MapEditPage, \"6DWkkKqkXBfu1U67WzK9u4fqsjk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations\n    ];\n});\n_c1 = MapEditPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"MindElixirReact\");\n$RefreshReg$(_c1, \"MapEditPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/edit/[id]/page.tsx\n"));

/***/ })

});