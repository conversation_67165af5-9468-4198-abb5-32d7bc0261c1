"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/edit/[id]/page",{

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js ***!
  \*******************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* reexport default from dynamic */ _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default.a; }\n/* harmony export */ });\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/lib/app-dynamic */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/app-dynamic.js\");\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceMappingURL=app-dynamic.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE0LjIuMjlfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9hcHAtZHluYW1pYy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEM7QUFDVTs7QUFFcEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTQuMi4yOV9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL2FwcC1keW5hbWljLmpzP2IwMDIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4uL3NoYXJlZC9saWIvYXBwLWR5bmFtaWNcIjtcbmV4cG9ydCB7IGRlZmF1bHQgfSBmcm9tIFwiLi4vc2hhcmVkL2xpYi9hcHAtZHluYW1pY1wiO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtZHluYW1pYy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/app-dynamic.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/app-dynamic.js ***!
  \**************************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return dynamic;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.5/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\"));\nconst _loadable = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./lazy-dynamic/loadable */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\"));\nfunction dynamic(dynamicOptions, options) {\n    var _mergedOptions_loadableGenerated;\n    let loadableOptions = {\n        // A loading component is not required, so we default it\n        loading: (param)=>{\n            let { error, isLoading, pastDelay } = param;\n            if (!pastDelay) return null;\n            if (true) {\n                if (isLoading) {\n                    return null;\n                }\n                if (error) {\n                    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                        children: [\n                            error.message,\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"br\", {}),\n                            error.stack\n                        ]\n                    });\n                }\n            }\n            return null;\n        }\n    };\n    if (typeof dynamicOptions === \"function\") {\n        loadableOptions.loader = dynamicOptions;\n    }\n    const mergedOptions = {\n        ...loadableOptions,\n        ...options\n    };\n    return (0, _loadable.default)({\n        ...mergedOptions,\n        modules: (_mergedOptions_loadableGenerated = mergedOptions.loadableGenerated) == null ? void 0 : _mergedOptions_loadableGenerated.modules\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dynamic.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE0LjIuMjlfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvYXBwLWR5bmFtaWMuanMiLCJtYXBwaW5ncyI6Ijs7OzsyQ0FpQ0E7OztlQUF3QkE7Ozs7OzRFQWpDTjsrRUFDRztBQWdDTixTQUFTQSxRQUN0QkMsY0FBNkMsRUFDN0NDLE9BQTJCO1FBbUNoQkM7SUFqQ1gsSUFBSUMsa0JBQXNDO1FBQ3hDLHdEQUF3RDtRQUN4REMsU0FBUyxDQUFBQztnQkFBQyxFQUFFQyxLQUFLLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFFLEdBQUFIO1lBQ3ZDLElBQUksQ0FBQ0csV0FBVyxPQUFPO1lBQ3ZCLElBQUlDLElBQXlCLEVBQWM7Z0JBQ3pDLElBQUlGLFdBQVc7b0JBQ2IsT0FBTztnQkFDVDtnQkFDQSxJQUFJRCxPQUFPO29CQUNULE9BQ0UsV0FERixHQUNFLElBQUFJLFlBQUFDLElBQUEsRUFBQ0MsS0FBQUE7OzRCQUNFTixNQUFNTyxPQUFPOzBDQUNkLElBQUFILFlBQUFJLEdBQUEsRUFBQ0MsTUFBQUEsQ0FBQUE7NEJBQ0FULE1BQU1VLEtBQUs7OztnQkFHbEI7WUFDRjtZQUNBLE9BQU87UUFDVDtJQUNGO0lBRUEsSUFBSSxPQUFPaEIsbUJBQW1CLFlBQVk7UUFDeENHLGdCQUFnQmMsTUFBTSxHQUFHakI7SUFDM0I7SUFFQSxNQUFNRSxnQkFBZ0I7UUFDcEIsR0FBR0MsZUFBZTtRQUNsQixHQUFHRixPQUFPO0lBQ1o7SUFFQSxPQUFPaUIsQ0FBQUEsR0FBQUEsVUFBQUEsT0FBUSxFQUFDO1FBQ2QsR0FBR2hCLGFBQWE7UUFDaEJpQixTQUFPLENBQUVqQixtQ0FBQUEsY0FBY2tCLGlCQUFpQixxQkFBL0JsQixpQ0FBaUNpQixPQUFPO0lBQ25EO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9zaGFyZWQvbGliL2FwcC1keW5hbWljLnRzeD85NDcwIl0sIm5hbWVzIjpbImR5bmFtaWMiLCJkeW5hbWljT3B0aW9ucyIsIm9wdGlvbnMiLCJtZXJnZWRPcHRpb25zIiwibG9hZGFibGVPcHRpb25zIiwibG9hZGluZyIsInBhcmFtIiwiZXJyb3IiLCJpc0xvYWRpbmciLCJwYXN0RGVsYXkiLCJwcm9jZXNzIiwiX2pzeHJ1bnRpbWUiLCJqc3hzIiwicCIsIm1lc3NhZ2UiLCJqc3giLCJiciIsInN0YWNrIiwibG9hZGVyIiwiTG9hZGFibGUiLCJtb2R1bGVzIiwibG9hZGFibGVHZW5lcmF0ZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":
/*!**************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js ***!
  \**************************************************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"BailoutToCSR\", ({\n    enumerable: true,\n    get: function() {\n        return BailoutToCSR;\n    }\n}));\nconst _bailouttocsr = __webpack_require__(/*! ./bailout-to-csr */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nfunction BailoutToCSR(param) {\n    let { reason, children } = param;\n    if (typeof window === \"undefined\") {\n        throw new _bailouttocsr.BailoutToCSRError(reason);\n    }\n    return children;\n} //# sourceMappingURL=dynamic-bailout-to-csr.js.map\n_c = BailoutToCSR;\nvar _c;\n$RefreshReg$(_c, \"BailoutToCSR\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE0LjIuMjlfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvbGF6eS1keW5hbWljL2R5bmFtaWMtYmFpbG91dC10by1jc3IuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQWNPLE1BQUFBLGdCQUFzQkMsbUJBQUFBLENBQXVDO1NBQXZDQyxhQUFVQyxLQUFRO0lBQzdDLElBQUksRUFBQUMsTUFBT0MsRUFBQUEsUUFBVyxLQUFBQztRQUNwQixPQUFNRCxXQUFJRSxhQUFBQTtRQUNaLFVBQUFQLGNBQUFPLGlCQUFBLENBQUFIO0lBRUE7SUFDRixPQUFBRDs7S0FONkJEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvc2hhcmVkL2xpYi9sYXp5LWR5bmFtaWMvZHluYW1pYy1iYWlsb3V0LXRvLWNzci50c3g/ZTI2NCJdLCJuYW1lcyI6WyJfYmFpbG91dHRvY3NyIiwicmVxdWlyZSIsIkJhaWxvdXRUb0NTUiIsImNoaWxkcmVuIiwicmVhc29uIiwid2luZG93IiwicGFyYW0iLCJCYWlsb3V0VG9DU1JFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js ***!
  \************************************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\nconst _dynamicbailouttocsr = __webpack_require__(/*! ./dynamic-bailout-to-csr */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\");\nconst _preloadcss = __webpack_require__(/*! ./preload-css */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js\");\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule(mod) {\n    // Check \"default\" prop before accessing it, as it could be client reference proxy that could break it reference.\n    // Cases:\n    // mod: { default: Component }\n    // mod: Component\n    // mod: { $$typeof, default: proxy(Component) }\n    // mod: proxy(Component)\n    const hasDefault = mod && \"default\" in mod;\n    return {\n        default: hasDefault ? mod.default : mod\n    };\n}\nconst defaultOptions = {\n    loader: ()=>Promise.resolve(convertModule(()=>null)),\n    loading: null,\n    ssr: true\n};\nfunction Loadable(options) {\n    const opts = {\n        ...defaultOptions,\n        ...options\n    };\n    const Lazy = /*#__PURE__*/ (0, _react.lazy)(()=>opts.loader().then(convertModule));\n    const Loading = opts.loading;\n    function LoadableComponent(props) {\n        const fallbackElement = Loading ? /*#__PURE__*/ (0, _jsxruntime.jsx)(Loading, {\n            isLoading: true,\n            pastDelay: true,\n            error: null\n        }) : null;\n        const children = opts.ssr ? /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n            children: [\n                typeof window === \"undefined\" ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_preloadcss.PreloadCss, {\n                    moduleIds: opts.modules\n                }) : null,\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {\n                    ...props\n                })\n            ]\n        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_dynamicbailouttocsr.BailoutToCSR, {\n            reason: \"next/dynamic\",\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {\n                ...props\n            })\n        });\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.Suspense, {\n            fallback: fallbackElement,\n            children: children\n        });\n    }\n    LoadableComponent.displayName = \"LoadableComponent\";\n    return LoadableComponent;\n}\n_c = Loadable;\nconst _default = Loadable; //# sourceMappingURL=loadable.js.map\nvar _c;\n$RefreshReg$(_c, \"Loadable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js ***!
  \***************************************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"PreloadCss\", ({\n    enumerable: true,\n    get: function() {\n        return PreloadCss;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _requestasyncstorageexternal = __webpack_require__(/*! ../../../client/components/request-async-storage.external */ \"(shared)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/request-async-storage.external.js\");\nfunction PreloadCss(param) {\n    let { moduleIds } = param;\n    // Early return in client compilation and only load requestStore on server side\n    if (typeof window !== \"undefined\") {\n        return null;\n    }\n    const requestStore = (0, _requestasyncstorageexternal.getExpectedRequestStore)(\"next/dynamic css\");\n    const allFiles = [];\n    // Search the current dynamic call unique key id in react loadable manifest,\n    // and find the corresponding CSS files to preload\n    if (requestStore.reactLoadableManifest && moduleIds) {\n        const manifest = requestStore.reactLoadableManifest;\n        for (const key of moduleIds){\n            if (!manifest[key]) continue;\n            const cssFiles = manifest[key].files.filter((file)=>file.endsWith(\".css\"));\n            allFiles.push(...cssFiles);\n        }\n    }\n    if (allFiles.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: allFiles.map((file)=>{\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                // @ts-ignore\n                precedence: \"dynamic\",\n                rel: \"stylesheet\",\n                href: requestStore.assetPrefix + \"/_next/\" + encodeURI(file),\n                as: \"style\"\n            }, file);\n        })\n    });\n} //# sourceMappingURL=preload-css.js.map\n_c = PreloadCss;\nvar _c;\n$RefreshReg$(_c, \"PreloadCss\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE0LjIuMjlfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvbGF6eS1keW5hbWljL3ByZWxvYWQtY3NzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBSU8sTUFBQUEsK0JBQXNFQyxtQkFBQUEsQ0FBQTtTQUFsREMsV0FBV0MsS0FBWDtJQUN6QixNQUFBQyxTQUFBLEtBQUFEO0lBQ0EsK0VBQW1DO1FBQ2pDLE9BQU9FLFdBQUE7UUFDVDtJQUVBO0lBQ0EsTUFBTUMsZUFBYSxJQUFBTiw2QkFBQU8sdUJBQUE7SUFFbkIsTUFBQUQsV0FBQTtJQUNBLDRFQUFrRDtJQUNsRCxrREFBMENGO1FBQ3hDSSxhQUFNQyxxQkFBd0JDLElBQUFBLFdBQUFBO1FBQzlCLE1BQUtELFdBQU1FLGFBQWtCRCxxQkFBQTthQUMzQixNQUFLRCxPQUFTRSxVQUFNO1lBQ3BCLEtBQUFGLFFBQU1HLENBQUFBLElBQVdILEVBQUFBO1lBR2pCSCxNQUFBQSxXQUFpQk0sUUFBQUEsQ0FBQUEsSUFBQUEsQ0FBQUEsS0FBQUEsQ0FBQUEsTUFBQUEsQ0FBQUEsQ0FBQUEsT0FBQUEsS0FBQUEsUUFBQUEsQ0FBQUE7WUFDbkJOLFNBQUFPLElBQUEsSUFBQUQ7UUFDRjtJQUVBO1FBQ0VOLFNBQU9RLE1BQUE7UUFDVDtJQUVBO1dBRUtSLFdBQUFBLEdBQUFBLENBQUFBLEdBQVNTLFlBQUtDLEdBQUFBLEVBQUFBLFlBQUFBLFFBQUFBLEVBQUFBO2tCQUNiVixTQUFBUyxHQUFBLEVBQUFDO21CQUdpQixrQkFBQUMsWUFBQUMsR0FBQTtnQkFDYkMsYUFBWTtnQkFDWkMsWUFBSTtnQkFDSkMsS0FBQUE7Z0JBQ0FDLE1BQUdkLGFBQUFlLFdBQUEsZUFBQUMsVUFBQVI7Z0JBTEVBLElBQUFBO1lBUVgsR0FBQUE7O0lBR047O0tBMUMyQmQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9zaGFyZWQvbGliL2xhenktZHluYW1pYy9wcmVsb2FkLWNzcy50c3g/M2U5MCJdLCJuYW1lcyI6WyJfcmVxdWVzdGFzeW5jc3RvcmFnZWV4dGVybmFsIiwicmVxdWlyZSIsIlByZWxvYWRDc3MiLCJwYXJhbSIsIm1vZHVsZUlkcyIsIndpbmRvdyIsImFsbEZpbGVzIiwiZ2V0RXhwZWN0ZWRSZXF1ZXN0U3RvcmUiLCJyZXF1ZXN0U3RvcmUiLCJtYW5pZmVzdCIsInJlYWN0TG9hZGFibGVNYW5pZmVzdCIsImtleSIsImNzc0ZpbGVzIiwicHVzaCIsImxlbmd0aCIsIm1hcCIsImZpbGUiLCJfanN4cnVudGltZSIsImpzeCIsInByZWNlZGVuY2UiLCJyZWwiLCJocmVmIiwiYXMiLCJhc3NldFByZWZpeCIsImVuY29kZVVSSSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/[locale]/edit/[id]/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/[locale]/edit/[id]/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MapEditPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _connect__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/connect */ \"(app-pages-browser)/./src/connect.ts\");\n/* harmony import */ var _utils_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/toast */ \"(app-pages-browser)/./src/utils/toast.ts\");\n/* harmony import */ var _mind_elixir_node_menu_neo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mind-elixir/node-menu-neo */ \"(app-pages-browser)/./node_modules/.pnpm/@mind-elixir+node-menu-neo@1.0.4/node_modules/@mind-elixir/node-menu-neo/dist/node-menu-neo.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// @ts-ignore\n\nconst MindElixirReact = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_MindElixirReact_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/MindElixirReact */ \"(app-pages-browser)/./src/components/MindElixirReact.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx -> \" + \"@/components/MindElixirReact\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex items-center justify-center\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n            lineNumber: 17,\n            columnNumber: 18\n        }, undefined)\n});\n_c = MindElixirReact;\nfunction MapEditPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"button\");\n    const [mapData, setMapData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [isUnsaved, setIsUnsaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSavedTime, setLastSavedTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const meRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const plugins = [\n        _mind_elixir_node_menu_neo__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n    const options = {\n        el: \"\",\n        direction: 2,\n        allowUndo: true\n    };\n    const mapId = params.id;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchMap = async ()=>{\n            try {\n                const res = await _connect__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/api/map/\".concat(mapId));\n                setMapData(res.data.content);\n            } catch (error) {\n                console.error(\"Failed to fetch map:\", error);\n                router.push(\"/404\");\n            }\n        };\n        if (mapId) {\n            fetchMap();\n        }\n    }, [\n        mapId,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _meRef_current;\n        const instance = (_meRef_current = meRef.current) === null || _meRef_current === void 0 ? void 0 : _meRef_current.instance;\n        if (instance) {\n            var _instance_map;\n            const handleOperation = ()=>{\n                setIsUnsaved(true);\n            };\n            const handleKeydown = (e)=>{\n                e.preventDefault();\n                if (e.target !== e.currentTarget) {\n                    return;\n                }\n                if (e.ctrlKey && e.key === \"s\") {\n                    save();\n                }\n            };\n            instance.bus.addListener(\"operation\", handleOperation);\n            (_instance_map = instance.map) === null || _instance_map === void 0 ? void 0 : _instance_map.addEventListener(\"keydown\", handleKeydown);\n            return ()=>{\n                var _instance_map;\n                instance.bus.removeListener(\"operation\", handleOperation);\n                (_instance_map = instance.map) === null || _instance_map === void 0 ? void 0 : _instance_map.removeEventListener(\"keydown\", handleKeydown);\n            };\n        }\n    }, [\n        mapData\n    ]);\n    const save = async ()=>{\n        var _meRef_current;\n        if (saving || !isUnsaved || !((_meRef_current = meRef.current) === null || _meRef_current === void 0 ? void 0 : _meRef_current.instance)) return;\n        setSaving(true);\n        try {\n            const newData = meRef.current.instance.getData();\n            newData.theme = undefined;\n            await _connect__WEBPACK_IMPORTED_MODULE_4__[\"default\"].patch(\"/api/map/\".concat(mapId), {\n                name: newData.nodeData.topic,\n                content: newData\n            });\n            setSaving(false);\n            setIsUnsaved(false);\n            setLastSavedTime(new Date().toLocaleString());\n            _utils_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"Saved\");\n        } catch (error) {\n            setSaving(false);\n            _utils_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Failed to save\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleBeforeUnload = (e)=>{\n            if (isUnsaved) {\n                e.preventDefault();\n                e.returnValue = \"\";\n            }\n        };\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return ()=>window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n    }, [\n        isUnsaved\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MindElixirReact, {\n                ref: meRef,\n                data: mapData,\n                plugins: plugins,\n                options: options,\n                className: \"h-screen\"\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            isUnsaved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-10 left-6 dark:text-gray-200\",\n                children: \"Unsaved\"\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this),\n            lastSavedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-6 left-6 dark:text-gray-200\",\n                children: [\n                    \"Last saved time: \",\n                    lastSavedTime\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-5 right-8 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"btn\",\n                    onClick: save,\n                    disabled: saving || !isUnsaved,\n                    children: [\n                        saving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"loading loading-spinner\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 22\n                        }, this),\n                        t(\"save\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(MapEditPage, \"6DWkkKqkXBfu1U67WzK9u4fqsjk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations\n    ];\n});\n_c1 = MapEditPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"MindElixirReact\");\n$RefreshReg$(_c1, \"MapEditPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/edit/[id]/page.tsx\n"));

/***/ })

});