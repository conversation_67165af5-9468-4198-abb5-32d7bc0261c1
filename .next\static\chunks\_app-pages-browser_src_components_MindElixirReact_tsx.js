/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_MindElixirReact_tsx"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/dompurify@3.1.7/node_modules/dompurify/dist/purify.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/dompurify@3.1.7/node_modules/dompurify/dist/purify.js ***!
  \**********************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("/*! @license DOMPurify 3.1.7 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.1.7/LICENSE */\n\n(function (global, factory) {\n   true ? module.exports = factory() :\n  0;\n})(this, (function () { 'use strict';\n\n  const {\n    entries,\n    setPrototypeOf,\n    isFrozen,\n    getPrototypeOf,\n    getOwnPropertyDescriptor\n  } = Object;\n  let {\n    freeze,\n    seal,\n    create\n  } = Object; // eslint-disable-line import/no-mutable-exports\n  let {\n    apply,\n    construct\n  } = typeof Reflect !== 'undefined' && Reflect;\n  if (!freeze) {\n    freeze = function freeze(x) {\n      return x;\n    };\n  }\n  if (!seal) {\n    seal = function seal(x) {\n      return x;\n    };\n  }\n  if (!apply) {\n    apply = function apply(fun, thisValue, args) {\n      return fun.apply(thisValue, args);\n    };\n  }\n  if (!construct) {\n    construct = function construct(Func, args) {\n      return new Func(...args);\n    };\n  }\n  const arrayForEach = unapply(Array.prototype.forEach);\n  const arrayPop = unapply(Array.prototype.pop);\n  const arrayPush = unapply(Array.prototype.push);\n  const stringToLowerCase = unapply(String.prototype.toLowerCase);\n  const stringToString = unapply(String.prototype.toString);\n  const stringMatch = unapply(String.prototype.match);\n  const stringReplace = unapply(String.prototype.replace);\n  const stringIndexOf = unapply(String.prototype.indexOf);\n  const stringTrim = unapply(String.prototype.trim);\n  const objectHasOwnProperty = unapply(Object.prototype.hasOwnProperty);\n  const regExpTest = unapply(RegExp.prototype.test);\n  const typeErrorCreate = unconstruct(TypeError);\n\n  /**\n   * Creates a new function that calls the given function with a specified thisArg and arguments.\n   *\n   * @param {Function} func - The function to be wrapped and called.\n   * @returns {Function} A new function that calls the given function with a specified thisArg and arguments.\n   */\n  function unapply(func) {\n    return function (thisArg) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      return apply(func, thisArg, args);\n    };\n  }\n\n  /**\n   * Creates a new function that constructs an instance of the given constructor function with the provided arguments.\n   *\n   * @param {Function} func - The constructor function to be wrapped and called.\n   * @returns {Function} A new function that constructs an instance of the given constructor function with the provided arguments.\n   */\n  function unconstruct(func) {\n    return function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      return construct(func, args);\n    };\n  }\n\n  /**\n   * Add properties to a lookup table\n   *\n   * @param {Object} set - The set to which elements will be added.\n   * @param {Array} array - The array containing elements to be added to the set.\n   * @param {Function} transformCaseFunc - An optional function to transform the case of each element before adding to the set.\n   * @returns {Object} The modified set with added elements.\n   */\n  function addToSet(set, array) {\n    let transformCaseFunc = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : stringToLowerCase;\n    if (setPrototypeOf) {\n      // Make 'in' and truthy checks like Boolean(set.constructor)\n      // independent of any properties defined on Object.prototype.\n      // Prevent prototype setters from intercepting set as a this value.\n      setPrototypeOf(set, null);\n    }\n    let l = array.length;\n    while (l--) {\n      let element = array[l];\n      if (typeof element === 'string') {\n        const lcElement = transformCaseFunc(element);\n        if (lcElement !== element) {\n          // Config presets (e.g. tags.js, attrs.js) are immutable.\n          if (!isFrozen(array)) {\n            array[l] = lcElement;\n          }\n          element = lcElement;\n        }\n      }\n      set[element] = true;\n    }\n    return set;\n  }\n\n  /**\n   * Clean up an array to harden against CSPP\n   *\n   * @param {Array} array - The array to be cleaned.\n   * @returns {Array} The cleaned version of the array\n   */\n  function cleanArray(array) {\n    for (let index = 0; index < array.length; index++) {\n      const isPropertyExist = objectHasOwnProperty(array, index);\n      if (!isPropertyExist) {\n        array[index] = null;\n      }\n    }\n    return array;\n  }\n\n  /**\n   * Shallow clone an object\n   *\n   * @param {Object} object - The object to be cloned.\n   * @returns {Object} A new object that copies the original.\n   */\n  function clone(object) {\n    const newObject = create(null);\n    for (const [property, value] of entries(object)) {\n      const isPropertyExist = objectHasOwnProperty(object, property);\n      if (isPropertyExist) {\n        if (Array.isArray(value)) {\n          newObject[property] = cleanArray(value);\n        } else if (value && typeof value === 'object' && value.constructor === Object) {\n          newObject[property] = clone(value);\n        } else {\n          newObject[property] = value;\n        }\n      }\n    }\n    return newObject;\n  }\n\n  /**\n   * This method automatically checks if the prop is function or getter and behaves accordingly.\n   *\n   * @param {Object} object - The object to look up the getter function in its prototype chain.\n   * @param {String} prop - The property name for which to find the getter function.\n   * @returns {Function} The getter function found in the prototype chain or a fallback function.\n   */\n  function lookupGetter(object, prop) {\n    while (object !== null) {\n      const desc = getOwnPropertyDescriptor(object, prop);\n      if (desc) {\n        if (desc.get) {\n          return unapply(desc.get);\n        }\n        if (typeof desc.value === 'function') {\n          return unapply(desc.value);\n        }\n      }\n      object = getPrototypeOf(object);\n    }\n    function fallbackValue() {\n      return null;\n    }\n    return fallbackValue;\n  }\n\n  const html$1 = freeze(['a', 'abbr', 'acronym', 'address', 'area', 'article', 'aside', 'audio', 'b', 'bdi', 'bdo', 'big', 'blink', 'blockquote', 'body', 'br', 'button', 'canvas', 'caption', 'center', 'cite', 'code', 'col', 'colgroup', 'content', 'data', 'datalist', 'dd', 'decorator', 'del', 'details', 'dfn', 'dialog', 'dir', 'div', 'dl', 'dt', 'element', 'em', 'fieldset', 'figcaption', 'figure', 'font', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'head', 'header', 'hgroup', 'hr', 'html', 'i', 'img', 'input', 'ins', 'kbd', 'label', 'legend', 'li', 'main', 'map', 'mark', 'marquee', 'menu', 'menuitem', 'meter', 'nav', 'nobr', 'ol', 'optgroup', 'option', 'output', 'p', 'picture', 'pre', 'progress', 'q', 'rp', 'rt', 'ruby', 's', 'samp', 'section', 'select', 'shadow', 'small', 'source', 'spacer', 'span', 'strike', 'strong', 'style', 'sub', 'summary', 'sup', 'table', 'tbody', 'td', 'template', 'textarea', 'tfoot', 'th', 'thead', 'time', 'tr', 'track', 'tt', 'u', 'ul', 'var', 'video', 'wbr']);\n\n  // SVG\n  const svg$1 = freeze(['svg', 'a', 'altglyph', 'altglyphdef', 'altglyphitem', 'animatecolor', 'animatemotion', 'animatetransform', 'circle', 'clippath', 'defs', 'desc', 'ellipse', 'filter', 'font', 'g', 'glyph', 'glyphref', 'hkern', 'image', 'line', 'lineargradient', 'marker', 'mask', 'metadata', 'mpath', 'path', 'pattern', 'polygon', 'polyline', 'radialgradient', 'rect', 'stop', 'style', 'switch', 'symbol', 'text', 'textpath', 'title', 'tref', 'tspan', 'view', 'vkern']);\n  const svgFilters = freeze(['feBlend', 'feColorMatrix', 'feComponentTransfer', 'feComposite', 'feConvolveMatrix', 'feDiffuseLighting', 'feDisplacementMap', 'feDistantLight', 'feDropShadow', 'feFlood', 'feFuncA', 'feFuncB', 'feFuncG', 'feFuncR', 'feGaussianBlur', 'feImage', 'feMerge', 'feMergeNode', 'feMorphology', 'feOffset', 'fePointLight', 'feSpecularLighting', 'feSpotLight', 'feTile', 'feTurbulence']);\n\n  // List of SVG elements that are disallowed by default.\n  // We still need to know them so that we can do namespace\n  // checks properly in case one wants to add them to\n  // allow-list.\n  const svgDisallowed = freeze(['animate', 'color-profile', 'cursor', 'discard', 'font-face', 'font-face-format', 'font-face-name', 'font-face-src', 'font-face-uri', 'foreignobject', 'hatch', 'hatchpath', 'mesh', 'meshgradient', 'meshpatch', 'meshrow', 'missing-glyph', 'script', 'set', 'solidcolor', 'unknown', 'use']);\n  const mathMl$1 = freeze(['math', 'menclose', 'merror', 'mfenced', 'mfrac', 'mglyph', 'mi', 'mlabeledtr', 'mmultiscripts', 'mn', 'mo', 'mover', 'mpadded', 'mphantom', 'mroot', 'mrow', 'ms', 'mspace', 'msqrt', 'mstyle', 'msub', 'msup', 'msubsup', 'mtable', 'mtd', 'mtext', 'mtr', 'munder', 'munderover', 'mprescripts']);\n\n  // Similarly to SVG, we want to know all MathML elements,\n  // even those that we disallow by default.\n  const mathMlDisallowed = freeze(['maction', 'maligngroup', 'malignmark', 'mlongdiv', 'mscarries', 'mscarry', 'msgroup', 'mstack', 'msline', 'msrow', 'semantics', 'annotation', 'annotation-xml', 'mprescripts', 'none']);\n  const text = freeze(['#text']);\n\n  const html = freeze(['accept', 'action', 'align', 'alt', 'autocapitalize', 'autocomplete', 'autopictureinpicture', 'autoplay', 'background', 'bgcolor', 'border', 'capture', 'cellpadding', 'cellspacing', 'checked', 'cite', 'class', 'clear', 'color', 'cols', 'colspan', 'controls', 'controlslist', 'coords', 'crossorigin', 'datetime', 'decoding', 'default', 'dir', 'disabled', 'disablepictureinpicture', 'disableremoteplayback', 'download', 'draggable', 'enctype', 'enterkeyhint', 'face', 'for', 'headers', 'height', 'hidden', 'high', 'href', 'hreflang', 'id', 'inputmode', 'integrity', 'ismap', 'kind', 'label', 'lang', 'list', 'loading', 'loop', 'low', 'max', 'maxlength', 'media', 'method', 'min', 'minlength', 'multiple', 'muted', 'name', 'nonce', 'noshade', 'novalidate', 'nowrap', 'open', 'optimum', 'pattern', 'placeholder', 'playsinline', 'popover', 'popovertarget', 'popovertargetaction', 'poster', 'preload', 'pubdate', 'radiogroup', 'readonly', 'rel', 'required', 'rev', 'reversed', 'role', 'rows', 'rowspan', 'spellcheck', 'scope', 'selected', 'shape', 'size', 'sizes', 'span', 'srclang', 'start', 'src', 'srcset', 'step', 'style', 'summary', 'tabindex', 'title', 'translate', 'type', 'usemap', 'valign', 'value', 'width', 'wrap', 'xmlns', 'slot']);\n  const svg = freeze(['accent-height', 'accumulate', 'additive', 'alignment-baseline', 'amplitude', 'ascent', 'attributename', 'attributetype', 'azimuth', 'basefrequency', 'baseline-shift', 'begin', 'bias', 'by', 'class', 'clip', 'clippathunits', 'clip-path', 'clip-rule', 'color', 'color-interpolation', 'color-interpolation-filters', 'color-profile', 'color-rendering', 'cx', 'cy', 'd', 'dx', 'dy', 'diffuseconstant', 'direction', 'display', 'divisor', 'dur', 'edgemode', 'elevation', 'end', 'exponent', 'fill', 'fill-opacity', 'fill-rule', 'filter', 'filterunits', 'flood-color', 'flood-opacity', 'font-family', 'font-size', 'font-size-adjust', 'font-stretch', 'font-style', 'font-variant', 'font-weight', 'fx', 'fy', 'g1', 'g2', 'glyph-name', 'glyphref', 'gradientunits', 'gradienttransform', 'height', 'href', 'id', 'image-rendering', 'in', 'in2', 'intercept', 'k', 'k1', 'k2', 'k3', 'k4', 'kerning', 'keypoints', 'keysplines', 'keytimes', 'lang', 'lengthadjust', 'letter-spacing', 'kernelmatrix', 'kernelunitlength', 'lighting-color', 'local', 'marker-end', 'marker-mid', 'marker-start', 'markerheight', 'markerunits', 'markerwidth', 'maskcontentunits', 'maskunits', 'max', 'mask', 'media', 'method', 'mode', 'min', 'name', 'numoctaves', 'offset', 'operator', 'opacity', 'order', 'orient', 'orientation', 'origin', 'overflow', 'paint-order', 'path', 'pathlength', 'patterncontentunits', 'patterntransform', 'patternunits', 'points', 'preservealpha', 'preserveaspectratio', 'primitiveunits', 'r', 'rx', 'ry', 'radius', 'refx', 'refy', 'repeatcount', 'repeatdur', 'restart', 'result', 'rotate', 'scale', 'seed', 'shape-rendering', 'slope', 'specularconstant', 'specularexponent', 'spreadmethod', 'startoffset', 'stddeviation', 'stitchtiles', 'stop-color', 'stop-opacity', 'stroke-dasharray', 'stroke-dashoffset', 'stroke-linecap', 'stroke-linejoin', 'stroke-miterlimit', 'stroke-opacity', 'stroke', 'stroke-width', 'style', 'surfacescale', 'systemlanguage', 'tabindex', 'tablevalues', 'targetx', 'targety', 'transform', 'transform-origin', 'text-anchor', 'text-decoration', 'text-rendering', 'textlength', 'type', 'u1', 'u2', 'unicode', 'values', 'viewbox', 'visibility', 'version', 'vert-adv-y', 'vert-origin-x', 'vert-origin-y', 'width', 'word-spacing', 'wrap', 'writing-mode', 'xchannelselector', 'ychannelselector', 'x', 'x1', 'x2', 'xmlns', 'y', 'y1', 'y2', 'z', 'zoomandpan']);\n  const mathMl = freeze(['accent', 'accentunder', 'align', 'bevelled', 'close', 'columnsalign', 'columnlines', 'columnspan', 'denomalign', 'depth', 'dir', 'display', 'displaystyle', 'encoding', 'fence', 'frame', 'height', 'href', 'id', 'largeop', 'length', 'linethickness', 'lspace', 'lquote', 'mathbackground', 'mathcolor', 'mathsize', 'mathvariant', 'maxsize', 'minsize', 'movablelimits', 'notation', 'numalign', 'open', 'rowalign', 'rowlines', 'rowspacing', 'rowspan', 'rspace', 'rquote', 'scriptlevel', 'scriptminsize', 'scriptsizemultiplier', 'selection', 'separator', 'separators', 'stretchy', 'subscriptshift', 'supscriptshift', 'symmetric', 'voffset', 'width', 'xmlns']);\n  const xml = freeze(['xlink:href', 'xml:id', 'xlink:title', 'xml:space', 'xmlns:xlink']);\n\n  // eslint-disable-next-line unicorn/better-regex\n  const MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\n  const ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\n  const TMPLIT_EXPR = seal(/\\${[\\w\\W]*}/gm);\n  const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]/); // eslint-disable-line no-useless-escape\n  const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\n  const IS_ALLOWED_URI = seal(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n  );\n  const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\n  const ATTR_WHITESPACE = seal(/[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n  );\n  const DOCTYPE_NAME = seal(/^html$/i);\n  const CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\n\n  var EXPRESSIONS = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    MUSTACHE_EXPR: MUSTACHE_EXPR,\n    ERB_EXPR: ERB_EXPR,\n    TMPLIT_EXPR: TMPLIT_EXPR,\n    DATA_ATTR: DATA_ATTR,\n    ARIA_ATTR: ARIA_ATTR,\n    IS_ALLOWED_URI: IS_ALLOWED_URI,\n    IS_SCRIPT_OR_DATA: IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE: ATTR_WHITESPACE,\n    DOCTYPE_NAME: DOCTYPE_NAME,\n    CUSTOM_ELEMENT: CUSTOM_ELEMENT\n  });\n\n  // https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\n  const NODE_TYPE = {\n    element: 1,\n    attribute: 2,\n    text: 3,\n    cdataSection: 4,\n    entityReference: 5,\n    // Deprecated\n    entityNode: 6,\n    // Deprecated\n    progressingInstruction: 7,\n    comment: 8,\n    document: 9,\n    documentType: 10,\n    documentFragment: 11,\n    notation: 12 // Deprecated\n  };\n  const getGlobal = function getGlobal() {\n    return typeof window === 'undefined' ? null : window;\n  };\n\n  /**\n   * Creates a no-op policy for internal use only.\n   * Don't export this function outside this module!\n   * @param {TrustedTypePolicyFactory} trustedTypes The policy factory.\n   * @param {HTMLScriptElement} purifyHostElement The Script element used to load DOMPurify (to determine policy name suffix).\n   * @return {TrustedTypePolicy} The policy created (or null, if Trusted Types\n   * are not supported or creating the policy failed).\n   */\n  const _createTrustedTypesPolicy = function _createTrustedTypesPolicy(trustedTypes, purifyHostElement) {\n    if (typeof trustedTypes !== 'object' || typeof trustedTypes.createPolicy !== 'function') {\n      return null;\n    }\n\n    // Allow the callers to control the unique policy name\n    // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n    // Policy creation with duplicate names throws in Trusted Types.\n    let suffix = null;\n    const ATTR_NAME = 'data-tt-policy-suffix';\n    if (purifyHostElement && purifyHostElement.hasAttribute(ATTR_NAME)) {\n      suffix = purifyHostElement.getAttribute(ATTR_NAME);\n    }\n    const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n    try {\n      return trustedTypes.createPolicy(policyName, {\n        createHTML(html) {\n          return html;\n        },\n        createScriptURL(scriptUrl) {\n          return scriptUrl;\n        }\n      });\n    } catch (_) {\n      // Policy creation failed (most likely another DOMPurify script has\n      // already run). Skip creating the policy, as this will only cause errors\n      // if TT are enforced.\n      console.warn('TrustedTypes policy ' + policyName + ' could not be created.');\n      return null;\n    }\n  };\n  function createDOMPurify() {\n    let window = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : getGlobal();\n    const DOMPurify = root => createDOMPurify(root);\n\n    /**\n     * Version label, exposed for easier checks\n     * if DOMPurify is up to date or not\n     */\n    DOMPurify.version = '3.1.7';\n\n    /**\n     * Array of elements that DOMPurify removed during sanitation.\n     * Empty if nothing was removed.\n     */\n    DOMPurify.removed = [];\n    if (!window || !window.document || window.document.nodeType !== NODE_TYPE.document) {\n      // Not running in a browser, provide a factory function\n      // so that you can pass your own Window\n      DOMPurify.isSupported = false;\n      return DOMPurify;\n    }\n    let {\n      document\n    } = window;\n    const originalDocument = document;\n    const currentScript = originalDocument.currentScript;\n    const {\n      DocumentFragment,\n      HTMLTemplateElement,\n      Node,\n      Element,\n      NodeFilter,\n      NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n      HTMLFormElement,\n      DOMParser,\n      trustedTypes\n    } = window;\n    const ElementPrototype = Element.prototype;\n    const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n    const remove = lookupGetter(ElementPrototype, 'remove');\n    const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n    const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n    const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n\n    // As per issue #47, the web-components registry is inherited by a\n    // new document created via createHTMLDocument. As per the spec\n    // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n    // a new empty registry is used when creating a template contents owner\n    // document, so we use that as our parent document to ensure nothing\n    // is inherited.\n    if (typeof HTMLTemplateElement === 'function') {\n      const template = document.createElement('template');\n      if (template.content && template.content.ownerDocument) {\n        document = template.content.ownerDocument;\n      }\n    }\n    let trustedTypesPolicy;\n    let emptyHTML = '';\n    const {\n      implementation,\n      createNodeIterator,\n      createDocumentFragment,\n      getElementsByTagName\n    } = document;\n    const {\n      importNode\n    } = originalDocument;\n    let hooks = {};\n\n    /**\n     * Expose whether this browser supports running the full DOMPurify.\n     */\n    DOMPurify.isSupported = typeof entries === 'function' && typeof getParentNode === 'function' && implementation && implementation.createHTMLDocument !== undefined;\n    const {\n      MUSTACHE_EXPR,\n      ERB_EXPR,\n      TMPLIT_EXPR,\n      DATA_ATTR,\n      ARIA_ATTR,\n      IS_SCRIPT_OR_DATA,\n      ATTR_WHITESPACE,\n      CUSTOM_ELEMENT\n    } = EXPRESSIONS;\n    let {\n      IS_ALLOWED_URI: IS_ALLOWED_URI$1\n    } = EXPRESSIONS;\n\n    /**\n     * We consider the elements and attributes below to be safe. Ideally\n     * don't add any new ones but feel free to remove unwanted ones.\n     */\n\n    /* allowed element names */\n    let ALLOWED_TAGS = null;\n    const DEFAULT_ALLOWED_TAGS = addToSet({}, [...html$1, ...svg$1, ...svgFilters, ...mathMl$1, ...text]);\n\n    /* Allowed attribute names */\n    let ALLOWED_ATTR = null;\n    const DEFAULT_ALLOWED_ATTR = addToSet({}, [...html, ...svg, ...mathMl, ...xml]);\n\n    /*\n     * Configure how DOMPUrify should handle custom elements and their attributes as well as customized built-in elements.\n     * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n     * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n     * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n     */\n    let CUSTOM_ELEMENT_HANDLING = Object.seal(create(null, {\n      tagNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null\n      },\n      attributeNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null\n      },\n      allowCustomizedBuiltInElements: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: false\n      }\n    }));\n\n    /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n    let FORBID_TAGS = null;\n\n    /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n    let FORBID_ATTR = null;\n\n    /* Decide if ARIA attributes are okay */\n    let ALLOW_ARIA_ATTR = true;\n\n    /* Decide if custom data attributes are okay */\n    let ALLOW_DATA_ATTR = true;\n\n    /* Decide if unknown protocols are okay */\n    let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n    /* Decide if self-closing tags in attributes are allowed.\n     * Usually removed due to a mXSS issue in jQuery 3.0 */\n    let ALLOW_SELF_CLOSE_IN_ATTR = true;\n\n    /* Output should be safe for common template engines.\n     * This means, DOMPurify removes data attributes, mustaches and ERB\n     */\n    let SAFE_FOR_TEMPLATES = false;\n\n    /* Output should be safe even for XML used within HTML and alike.\n     * This means, DOMPurify removes comments when containing risky content.\n     */\n    let SAFE_FOR_XML = true;\n\n    /* Decide if document with <html>... should be returned */\n    let WHOLE_DOCUMENT = false;\n\n    /* Track whether config is already set on this instance of DOMPurify. */\n    let SET_CONFIG = false;\n\n    /* Decide if all elements (e.g. style, script) must be children of\n     * document.body. By default, browsers might move them to document.head */\n    let FORCE_BODY = false;\n\n    /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n     * string (or a TrustedHTML object if Trusted Types are supported).\n     * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n     */\n    let RETURN_DOM = false;\n\n    /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n     * string  (or a TrustedHTML object if Trusted Types are supported) */\n    let RETURN_DOM_FRAGMENT = false;\n\n    /* Try to return a Trusted Type object instead of a string, return a string in\n     * case Trusted Types are not supported  */\n    let RETURN_TRUSTED_TYPE = false;\n\n    /* Output should be free from DOM clobbering attacks?\n     * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n     */\n    let SANITIZE_DOM = true;\n\n    /* Achieve full DOM Clobbering protection by isolating the namespace of named\n     * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n     *\n     * HTML/DOM spec rules that enable DOM Clobbering:\n     *   - Named Access on Window (§7.3.3)\n     *   - DOM Tree Accessors (§3.1.5)\n     *   - Form Element Parent-Child Relations (§4.10.3)\n     *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n     *   - HTMLCollection (§4.2.10.2)\n     *\n     * Namespace isolation is implemented by prefixing `id` and `name` attributes\n     * with a constant string, i.e., `user-content-`\n     */\n    let SANITIZE_NAMED_PROPS = false;\n    const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n\n    /* Keep element content when removing element? */\n    let KEEP_CONTENT = true;\n\n    /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n     * of importing it into a new Document and returning a sanitized copy */\n    let IN_PLACE = false;\n\n    /* Allow usage of profiles like html, svg and mathMl */\n    let USE_PROFILES = {};\n\n    /* Tags to ignore content of when KEEP_CONTENT is true */\n    let FORBID_CONTENTS = null;\n    const DEFAULT_FORBID_CONTENTS = addToSet({}, ['annotation-xml', 'audio', 'colgroup', 'desc', 'foreignobject', 'head', 'iframe', 'math', 'mi', 'mn', 'mo', 'ms', 'mtext', 'noembed', 'noframes', 'noscript', 'plaintext', 'script', 'style', 'svg', 'template', 'thead', 'title', 'video', 'xmp']);\n\n    /* Tags that are safe for data: URIs */\n    let DATA_URI_TAGS = null;\n    const DEFAULT_DATA_URI_TAGS = addToSet({}, ['audio', 'video', 'img', 'source', 'image', 'track']);\n\n    /* Attributes safe for values like \"javascript:\" */\n    let URI_SAFE_ATTRIBUTES = null;\n    const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, ['alt', 'class', 'for', 'id', 'label', 'name', 'pattern', 'placeholder', 'role', 'summary', 'title', 'value', 'style', 'xmlns']);\n    const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n    const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n    const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n    /* Document namespace */\n    let NAMESPACE = HTML_NAMESPACE;\n    let IS_EMPTY_INPUT = false;\n\n    /* Allowed XHTML+XML namespaces */\n    let ALLOWED_NAMESPACES = null;\n    const DEFAULT_ALLOWED_NAMESPACES = addToSet({}, [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE], stringToString);\n\n    /* Parsing of strict XHTML documents */\n    let PARSER_MEDIA_TYPE = null;\n    const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n    const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n    let transformCaseFunc = null;\n\n    /* Keep a reference to config to pass to hooks */\n    let CONFIG = null;\n\n    /* Ideally, do not touch anything below this line */\n    /* ______________________________________________ */\n\n    const formElement = document.createElement('form');\n    const isRegexOrFunction = function isRegexOrFunction(testValue) {\n      return testValue instanceof RegExp || testValue instanceof Function;\n    };\n\n    /**\n     * _parseConfig\n     *\n     * @param  {Object} cfg optional config literal\n     */\n    // eslint-disable-next-line complexity\n    const _parseConfig = function _parseConfig() {\n      let cfg = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      if (CONFIG && CONFIG === cfg) {\n        return;\n      }\n\n      /* Shield configuration object from tampering */\n      if (!cfg || typeof cfg !== 'object') {\n        cfg = {};\n      }\n\n      /* Shield configuration object from prototype pollution */\n      cfg = clone(cfg);\n      PARSER_MEDIA_TYPE =\n      // eslint-disable-next-line unicorn/prefer-includes\n      SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1 ? DEFAULT_PARSER_MEDIA_TYPE : cfg.PARSER_MEDIA_TYPE;\n\n      // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n      transformCaseFunc = PARSER_MEDIA_TYPE === 'application/xhtml+xml' ? stringToString : stringToLowerCase;\n\n      /* Set configuration parameters */\n      ALLOWED_TAGS = objectHasOwnProperty(cfg, 'ALLOWED_TAGS') ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc) : DEFAULT_ALLOWED_TAGS;\n      ALLOWED_ATTR = objectHasOwnProperty(cfg, 'ALLOWED_ATTR') ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc) : DEFAULT_ALLOWED_ATTR;\n      ALLOWED_NAMESPACES = objectHasOwnProperty(cfg, 'ALLOWED_NAMESPACES') ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString) : DEFAULT_ALLOWED_NAMESPACES;\n      URI_SAFE_ATTRIBUTES = objectHasOwnProperty(cfg, 'ADD_URI_SAFE_ATTR') ? addToSet(clone(DEFAULT_URI_SAFE_ATTRIBUTES),\n      // eslint-disable-line indent\n      cfg.ADD_URI_SAFE_ATTR,\n      // eslint-disable-line indent\n      transformCaseFunc // eslint-disable-line indent\n      ) // eslint-disable-line indent\n      : DEFAULT_URI_SAFE_ATTRIBUTES;\n      DATA_URI_TAGS = objectHasOwnProperty(cfg, 'ADD_DATA_URI_TAGS') ? addToSet(clone(DEFAULT_DATA_URI_TAGS),\n      // eslint-disable-line indent\n      cfg.ADD_DATA_URI_TAGS,\n      // eslint-disable-line indent\n      transformCaseFunc // eslint-disable-line indent\n      ) // eslint-disable-line indent\n      : DEFAULT_DATA_URI_TAGS;\n      FORBID_CONTENTS = objectHasOwnProperty(cfg, 'FORBID_CONTENTS') ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc) : DEFAULT_FORBID_CONTENTS;\n      FORBID_TAGS = objectHasOwnProperty(cfg, 'FORBID_TAGS') ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc) : {};\n      FORBID_ATTR = objectHasOwnProperty(cfg, 'FORBID_ATTR') ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc) : {};\n      USE_PROFILES = objectHasOwnProperty(cfg, 'USE_PROFILES') ? cfg.USE_PROFILES : false;\n      ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n      ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n      ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n      ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n      SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n      SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n      WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n      RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n      RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n      RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n      FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n      SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n      SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n      KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n      IN_PLACE = cfg.IN_PLACE || false; // Default false\n      IS_ALLOWED_URI$1 = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI;\n      NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n      CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n      if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)) {\n        CUSTOM_ELEMENT_HANDLING.tagNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n      }\n      if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)) {\n        CUSTOM_ELEMENT_HANDLING.attributeNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n      }\n      if (cfg.CUSTOM_ELEMENT_HANDLING && typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements === 'boolean') {\n        CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements = cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n      }\n      if (SAFE_FOR_TEMPLATES) {\n        ALLOW_DATA_ATTR = false;\n      }\n      if (RETURN_DOM_FRAGMENT) {\n        RETURN_DOM = true;\n      }\n\n      /* Parse profile info */\n      if (USE_PROFILES) {\n        ALLOWED_TAGS = addToSet({}, text);\n        ALLOWED_ATTR = [];\n        if (USE_PROFILES.html === true) {\n          addToSet(ALLOWED_TAGS, html$1);\n          addToSet(ALLOWED_ATTR, html);\n        }\n        if (USE_PROFILES.svg === true) {\n          addToSet(ALLOWED_TAGS, svg$1);\n          addToSet(ALLOWED_ATTR, svg);\n          addToSet(ALLOWED_ATTR, xml);\n        }\n        if (USE_PROFILES.svgFilters === true) {\n          addToSet(ALLOWED_TAGS, svgFilters);\n          addToSet(ALLOWED_ATTR, svg);\n          addToSet(ALLOWED_ATTR, xml);\n        }\n        if (USE_PROFILES.mathMl === true) {\n          addToSet(ALLOWED_TAGS, mathMl$1);\n          addToSet(ALLOWED_ATTR, mathMl);\n          addToSet(ALLOWED_ATTR, xml);\n        }\n      }\n\n      /* Merge configuration parameters */\n      if (cfg.ADD_TAGS) {\n        if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n          ALLOWED_TAGS = clone(ALLOWED_TAGS);\n        }\n        addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n      }\n      if (cfg.ADD_ATTR) {\n        if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n          ALLOWED_ATTR = clone(ALLOWED_ATTR);\n        }\n        addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n      }\n      if (cfg.ADD_URI_SAFE_ATTR) {\n        addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n      }\n      if (cfg.FORBID_CONTENTS) {\n        if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n          FORBID_CONTENTS = clone(FORBID_CONTENTS);\n        }\n        addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n      }\n\n      /* Add #text in case KEEP_CONTENT is set to true */\n      if (KEEP_CONTENT) {\n        ALLOWED_TAGS['#text'] = true;\n      }\n\n      /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n      if (WHOLE_DOCUMENT) {\n        addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n      }\n\n      /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n      if (ALLOWED_TAGS.table) {\n        addToSet(ALLOWED_TAGS, ['tbody']);\n        delete FORBID_TAGS.tbody;\n      }\n      if (cfg.TRUSTED_TYPES_POLICY) {\n        if (typeof cfg.TRUSTED_TYPES_POLICY.createHTML !== 'function') {\n          throw typeErrorCreate('TRUSTED_TYPES_POLICY configuration option must provide a \"createHTML\" hook.');\n        }\n        if (typeof cfg.TRUSTED_TYPES_POLICY.createScriptURL !== 'function') {\n          throw typeErrorCreate('TRUSTED_TYPES_POLICY configuration option must provide a \"createScriptURL\" hook.');\n        }\n\n        // Overwrite existing TrustedTypes policy.\n        trustedTypesPolicy = cfg.TRUSTED_TYPES_POLICY;\n\n        // Sign local variables required by `sanitize`.\n        emptyHTML = trustedTypesPolicy.createHTML('');\n      } else {\n        // Uninitialized policy, attempt to initialize the internal dompurify policy.\n        if (trustedTypesPolicy === undefined) {\n          trustedTypesPolicy = _createTrustedTypesPolicy(trustedTypes, currentScript);\n        }\n\n        // If creating the internal policy succeeded sign internal variables.\n        if (trustedTypesPolicy !== null && typeof emptyHTML === 'string') {\n          emptyHTML = trustedTypesPolicy.createHTML('');\n        }\n      }\n\n      // Prevent further manipulation of configuration.\n      // Not available in IE8, Safari 5, etc.\n      if (freeze) {\n        freeze(cfg);\n      }\n      CONFIG = cfg;\n    };\n    const MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, ['mi', 'mo', 'mn', 'ms', 'mtext']);\n    const HTML_INTEGRATION_POINTS = addToSet({}, ['annotation-xml']);\n\n    // Certain elements are allowed in both SVG and HTML\n    // namespace. We need to specify them explicitly\n    // so that they don't get erroneously deleted from\n    // HTML namespace.\n    const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, ['title', 'style', 'font', 'a', 'script']);\n\n    /* Keep track of all possible SVG and MathML tags\n     * so that we can perform the namespace checks\n     * correctly. */\n    const ALL_SVG_TAGS = addToSet({}, [...svg$1, ...svgFilters, ...svgDisallowed]);\n    const ALL_MATHML_TAGS = addToSet({}, [...mathMl$1, ...mathMlDisallowed]);\n\n    /**\n     * @param  {Element} element a DOM element whose namespace is being checked\n     * @returns {boolean} Return false if the element has a\n     *  namespace that a spec-compliant parser would never\n     *  return. Return true otherwise.\n     */\n    const _checkValidNamespace = function _checkValidNamespace(element) {\n      let parent = getParentNode(element);\n\n      // In JSDOM, if we're inside shadow DOM, then parentNode\n      // can be null. We just simulate parent in this case.\n      if (!parent || !parent.tagName) {\n        parent = {\n          namespaceURI: NAMESPACE,\n          tagName: 'template'\n        };\n      }\n      const tagName = stringToLowerCase(element.tagName);\n      const parentTagName = stringToLowerCase(parent.tagName);\n      if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n        return false;\n      }\n      if (element.namespaceURI === SVG_NAMESPACE) {\n        // The only way to switch from HTML namespace to SVG\n        // is via <svg>. If it happens via any other tag, then\n        // it should be killed.\n        if (parent.namespaceURI === HTML_NAMESPACE) {\n          return tagName === 'svg';\n        }\n\n        // The only way to switch from MathML to SVG is via`\n        // svg if parent is either <annotation-xml> or MathML\n        // text integration points.\n        if (parent.namespaceURI === MATHML_NAMESPACE) {\n          return tagName === 'svg' && (parentTagName === 'annotation-xml' || MATHML_TEXT_INTEGRATION_POINTS[parentTagName]);\n        }\n\n        // We only allow elements that are defined in SVG\n        // spec. All others are disallowed in SVG namespace.\n        return Boolean(ALL_SVG_TAGS[tagName]);\n      }\n      if (element.namespaceURI === MATHML_NAMESPACE) {\n        // The only way to switch from HTML namespace to MathML\n        // is via <math>. If it happens via any other tag, then\n        // it should be killed.\n        if (parent.namespaceURI === HTML_NAMESPACE) {\n          return tagName === 'math';\n        }\n\n        // The only way to switch from SVG to MathML is via\n        // <math> and HTML integration points\n        if (parent.namespaceURI === SVG_NAMESPACE) {\n          return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n        }\n\n        // We only allow elements that are defined in MathML\n        // spec. All others are disallowed in MathML namespace.\n        return Boolean(ALL_MATHML_TAGS[tagName]);\n      }\n      if (element.namespaceURI === HTML_NAMESPACE) {\n        // The only way to switch from SVG to HTML is via\n        // HTML integration points, and from MathML to HTML\n        // is via MathML text integration points\n        if (parent.namespaceURI === SVG_NAMESPACE && !HTML_INTEGRATION_POINTS[parentTagName]) {\n          return false;\n        }\n        if (parent.namespaceURI === MATHML_NAMESPACE && !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]) {\n          return false;\n        }\n\n        // We disallow tags that are specific for MathML\n        // or SVG and should never appear in HTML namespace\n        return !ALL_MATHML_TAGS[tagName] && (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName]);\n      }\n\n      // For XHTML and XML documents that support custom namespaces\n      if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && ALLOWED_NAMESPACES[element.namespaceURI]) {\n        return true;\n      }\n\n      // The code should never reach this place (this means\n      // that the element somehow got namespace that is not\n      // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n      // Return false just in case.\n      return false;\n    };\n\n    /**\n     * _forceRemove\n     *\n     * @param  {Node} node a DOM node\n     */\n    const _forceRemove = function _forceRemove(node) {\n      arrayPush(DOMPurify.removed, {\n        element: node\n      });\n      try {\n        // eslint-disable-next-line unicorn/prefer-dom-node-remove\n        getParentNode(node).removeChild(node);\n      } catch (_) {\n        remove(node);\n      }\n    };\n\n    /**\n     * _removeAttribute\n     *\n     * @param  {String} name an Attribute name\n     * @param  {Node} node a DOM node\n     */\n    const _removeAttribute = function _removeAttribute(name, node) {\n      try {\n        arrayPush(DOMPurify.removed, {\n          attribute: node.getAttributeNode(name),\n          from: node\n        });\n      } catch (_) {\n        arrayPush(DOMPurify.removed, {\n          attribute: null,\n          from: node\n        });\n      }\n      node.removeAttribute(name);\n\n      // We void attribute values for unremovable \"is\"\" attributes\n      if (name === 'is' && !ALLOWED_ATTR[name]) {\n        if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n          try {\n            _forceRemove(node);\n          } catch (_) {}\n        } else {\n          try {\n            node.setAttribute(name, '');\n          } catch (_) {}\n        }\n      }\n    };\n\n    /**\n     * _initDocument\n     *\n     * @param  {String} dirty a string of dirty markup\n     * @return {Document} a DOM, filled with the dirty markup\n     */\n    const _initDocument = function _initDocument(dirty) {\n      /* Create a HTML document */\n      let doc = null;\n      let leadingWhitespace = null;\n      if (FORCE_BODY) {\n        dirty = '<remove></remove>' + dirty;\n      } else {\n        /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n        const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n        leadingWhitespace = matches && matches[0];\n      }\n      if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && NAMESPACE === HTML_NAMESPACE) {\n        // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n        dirty = '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' + dirty + '</body></html>';\n      }\n      const dirtyPayload = trustedTypesPolicy ? trustedTypesPolicy.createHTML(dirty) : dirty;\n      /*\n       * Use the DOMParser API by default, fallback later if needs be\n       * DOMParser not work for svg when has multiple root element.\n       */\n      if (NAMESPACE === HTML_NAMESPACE) {\n        try {\n          doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n        } catch (_) {}\n      }\n\n      /* Use createHTMLDocument in case DOMParser is not available */\n      if (!doc || !doc.documentElement) {\n        doc = implementation.createDocument(NAMESPACE, 'template', null);\n        try {\n          doc.documentElement.innerHTML = IS_EMPTY_INPUT ? emptyHTML : dirtyPayload;\n        } catch (_) {\n          // Syntax error if dirtyPayload is invalid xml\n        }\n      }\n      const body = doc.body || doc.documentElement;\n      if (dirty && leadingWhitespace) {\n        body.insertBefore(document.createTextNode(leadingWhitespace), body.childNodes[0] || null);\n      }\n\n      /* Work on whole document or just its body */\n      if (NAMESPACE === HTML_NAMESPACE) {\n        return getElementsByTagName.call(doc, WHOLE_DOCUMENT ? 'html' : 'body')[0];\n      }\n      return WHOLE_DOCUMENT ? doc.documentElement : body;\n    };\n\n    /**\n     * Creates a NodeIterator object that you can use to traverse filtered lists of nodes or elements in a document.\n     *\n     * @param  {Node} root The root element or node to start traversing on.\n     * @return {NodeIterator} The created NodeIterator\n     */\n    const _createNodeIterator = function _createNodeIterator(root) {\n      return createNodeIterator.call(root.ownerDocument || root, root,\n      // eslint-disable-next-line no-bitwise\n      NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_COMMENT | NodeFilter.SHOW_TEXT | NodeFilter.SHOW_PROCESSING_INSTRUCTION | NodeFilter.SHOW_CDATA_SECTION, null);\n    };\n\n    /**\n     * _isClobbered\n     *\n     * @param  {Node} elm element to check for clobbering attacks\n     * @return {Boolean} true if clobbered, false if safe\n     */\n    const _isClobbered = function _isClobbered(elm) {\n      return elm instanceof HTMLFormElement && (typeof elm.nodeName !== 'string' || typeof elm.textContent !== 'string' || typeof elm.removeChild !== 'function' || !(elm.attributes instanceof NamedNodeMap) || typeof elm.removeAttribute !== 'function' || typeof elm.setAttribute !== 'function' || typeof elm.namespaceURI !== 'string' || typeof elm.insertBefore !== 'function' || typeof elm.hasChildNodes !== 'function');\n    };\n\n    /**\n     * Checks whether the given object is a DOM node.\n     *\n     * @param  {Node} object object to check whether it's a DOM node\n     * @return {Boolean} true is object is a DOM node\n     */\n    const _isNode = function _isNode(object) {\n      return typeof Node === 'function' && object instanceof Node;\n    };\n\n    /**\n     * _executeHook\n     * Execute user configurable hooks\n     *\n     * @param  {String} entryPoint  Name of the hook's entry point\n     * @param  {Node} currentNode node to work on with the hook\n     * @param  {Object} data additional hook parameters\n     */\n    const _executeHook = function _executeHook(entryPoint, currentNode, data) {\n      if (!hooks[entryPoint]) {\n        return;\n      }\n      arrayForEach(hooks[entryPoint], hook => {\n        hook.call(DOMPurify, currentNode, data, CONFIG);\n      });\n    };\n\n    /**\n     * _sanitizeElements\n     *\n     * @protect nodeName\n     * @protect textContent\n     * @protect removeChild\n     *\n     * @param   {Node} currentNode to check for permission to exist\n     * @return  {Boolean} true if node was killed, false if left alive\n     */\n    const _sanitizeElements = function _sanitizeElements(currentNode) {\n      let content = null;\n\n      /* Execute a hook if present */\n      _executeHook('beforeSanitizeElements', currentNode, null);\n\n      /* Check if element is clobbered or can clobber */\n      if (_isClobbered(currentNode)) {\n        _forceRemove(currentNode);\n        return true;\n      }\n\n      /* Now let's check the element's type and name */\n      const tagName = transformCaseFunc(currentNode.nodeName);\n\n      /* Execute a hook if present */\n      _executeHook('uponSanitizeElement', currentNode, {\n        tagName,\n        allowedTags: ALLOWED_TAGS\n      });\n\n      /* Detect mXSS attempts abusing namespace confusion */\n      if (currentNode.hasChildNodes() && !_isNode(currentNode.firstElementChild) && regExpTest(/<[/\\w]/g, currentNode.innerHTML) && regExpTest(/<[/\\w]/g, currentNode.textContent)) {\n        _forceRemove(currentNode);\n        return true;\n      }\n\n      /* Remove any occurrence of processing instructions */\n      if (currentNode.nodeType === NODE_TYPE.progressingInstruction) {\n        _forceRemove(currentNode);\n        return true;\n      }\n\n      /* Remove any kind of possibly harmful comments */\n      if (SAFE_FOR_XML && currentNode.nodeType === NODE_TYPE.comment && regExpTest(/<[/\\w]/g, currentNode.data)) {\n        _forceRemove(currentNode);\n        return true;\n      }\n\n      /* Remove element if anything forbids its presence */\n      if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n        /* Check if we have a custom element to handle */\n        if (!FORBID_TAGS[tagName] && _isBasicCustomElement(tagName)) {\n          if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)) {\n            return false;\n          }\n          if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)) {\n            return false;\n          }\n        }\n\n        /* Keep content except for bad-listed elements */\n        if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n          const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n          const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n          if (childNodes && parentNode) {\n            const childCount = childNodes.length;\n            for (let i = childCount - 1; i >= 0; --i) {\n              const childClone = cloneNode(childNodes[i], true);\n              childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n              parentNode.insertBefore(childClone, getNextSibling(currentNode));\n            }\n          }\n        }\n        _forceRemove(currentNode);\n        return true;\n      }\n\n      /* Check whether element has a valid namespace */\n      if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n        _forceRemove(currentNode);\n        return true;\n      }\n\n      /* Make sure that older browsers don't get fallback-tag mXSS */\n      if ((tagName === 'noscript' || tagName === 'noembed' || tagName === 'noframes') && regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)) {\n        _forceRemove(currentNode);\n        return true;\n      }\n\n      /* Sanitize element content to be template-safe */\n      if (SAFE_FOR_TEMPLATES && currentNode.nodeType === NODE_TYPE.text) {\n        /* Get the element's text content */\n        content = currentNode.textContent;\n        arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], expr => {\n          content = stringReplace(content, expr, ' ');\n        });\n        if (currentNode.textContent !== content) {\n          arrayPush(DOMPurify.removed, {\n            element: currentNode.cloneNode()\n          });\n          currentNode.textContent = content;\n        }\n      }\n\n      /* Execute a hook if present */\n      _executeHook('afterSanitizeElements', currentNode, null);\n      return false;\n    };\n\n    /**\n     * _isValidAttribute\n     *\n     * @param  {string} lcTag Lowercase tag name of containing element.\n     * @param  {string} lcName Lowercase attribute name.\n     * @param  {string} value Attribute value.\n     * @return {Boolean} Returns true if `value` is valid, otherwise false.\n     */\n    // eslint-disable-next-line complexity\n    const _isValidAttribute = function _isValidAttribute(lcTag, lcName, value) {\n      /* Make sure attribute cannot clobber */\n      if (SANITIZE_DOM && (lcName === 'id' || lcName === 'name') && (value in document || value in formElement)) {\n        return false;\n      }\n\n      /* Allow valid data-* attributes: At least one character after \"-\"\n          (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n          XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n          We don't need to check the value; it's always URI safe. */\n      if (ALLOW_DATA_ATTR && !FORBID_ATTR[lcName] && regExpTest(DATA_ATTR, lcName)) ; else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) ; else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n        if (\n        // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n        // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n        _isBasicCustomElement(lcTag) && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag)) && (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName) || CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)) ||\n        // Alternative, second condition checks if it's an `is`-attribute, AND\n        // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        lcName === 'is' && CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))) ; else {\n          return false;\n        }\n        /* Check value is safe. First, is attr inert? If so, is safe */\n      } else if (URI_SAFE_ATTRIBUTES[lcName]) ; else if (regExpTest(IS_ALLOWED_URI$1, stringReplace(value, ATTR_WHITESPACE, ''))) ; else if ((lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') && lcTag !== 'script' && stringIndexOf(value, 'data:') === 0 && DATA_URI_TAGS[lcTag]) ; else if (ALLOW_UNKNOWN_PROTOCOLS && !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))) ; else if (value) {\n        return false;\n      } else ;\n      return true;\n    };\n\n    /**\n     * _isBasicCustomElement\n     * checks if at least one dash is included in tagName, and it's not the first char\n     * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n     *\n     * @param {string} tagName name of the tag of the node to sanitize\n     * @returns {boolean} Returns true if the tag name meets the basic criteria for a custom element, otherwise false.\n     */\n    const _isBasicCustomElement = function _isBasicCustomElement(tagName) {\n      return tagName !== 'annotation-xml' && stringMatch(tagName, CUSTOM_ELEMENT);\n    };\n\n    /**\n     * _sanitizeAttributes\n     *\n     * @protect attributes\n     * @protect nodeName\n     * @protect removeAttribute\n     * @protect setAttribute\n     *\n     * @param  {Node} currentNode to sanitize\n     */\n    const _sanitizeAttributes = function _sanitizeAttributes(currentNode) {\n      /* Execute a hook if present */\n      _executeHook('beforeSanitizeAttributes', currentNode, null);\n      const {\n        attributes\n      } = currentNode;\n\n      /* Check if we have attributes; if not we might have a text node */\n      if (!attributes) {\n        return;\n      }\n      const hookEvent = {\n        attrName: '',\n        attrValue: '',\n        keepAttr: true,\n        allowedAttributes: ALLOWED_ATTR\n      };\n      let l = attributes.length;\n\n      /* Go backwards over all attributes; safely remove bad ones */\n      while (l--) {\n        const attr = attributes[l];\n        const {\n          name,\n          namespaceURI,\n          value: attrValue\n        } = attr;\n        const lcName = transformCaseFunc(name);\n        let value = name === 'value' ? attrValue : stringTrim(attrValue);\n\n        /* Execute a hook if present */\n        hookEvent.attrName = lcName;\n        hookEvent.attrValue = value;\n        hookEvent.keepAttr = true;\n        hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n        _executeHook('uponSanitizeAttribute', currentNode, hookEvent);\n        value = hookEvent.attrValue;\n\n        /* Did the hooks approve of the attribute? */\n        if (hookEvent.forceKeepAttr) {\n          continue;\n        }\n\n        /* Remove attribute */\n        _removeAttribute(name, currentNode);\n\n        /* Did the hooks approve of the attribute? */\n        if (!hookEvent.keepAttr) {\n          continue;\n        }\n\n        /* Work around a security issue in jQuery 3.0 */\n        if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n          _removeAttribute(name, currentNode);\n          continue;\n        }\n\n        /* Sanitize attribute content to be template-safe */\n        if (SAFE_FOR_TEMPLATES) {\n          arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], expr => {\n            value = stringReplace(value, expr, ' ');\n          });\n        }\n\n        /* Is `value` valid for this attribute? */\n        const lcTag = transformCaseFunc(currentNode.nodeName);\n        if (!_isValidAttribute(lcTag, lcName, value)) {\n          continue;\n        }\n\n        /* Full DOM Clobbering protection via namespace isolation,\n         * Prefix id and name attributes with `user-content-`\n         */\n        if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n          // Remove the attribute with this value\n          _removeAttribute(name, currentNode);\n\n          // Prefix the value and later re-create the attribute with the sanitized value\n          value = SANITIZE_NAMED_PROPS_PREFIX + value;\n        }\n\n        /* Work around a security issue with comments inside attributes */\n        if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n          _removeAttribute(name, currentNode);\n          continue;\n        }\n\n        /* Handle attributes that require Trusted Types */\n        if (trustedTypesPolicy && typeof trustedTypes === 'object' && typeof trustedTypes.getAttributeType === 'function') {\n          if (namespaceURI) ; else {\n            switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n              case 'TrustedHTML':\n                {\n                  value = trustedTypesPolicy.createHTML(value);\n                  break;\n                }\n              case 'TrustedScriptURL':\n                {\n                  value = trustedTypesPolicy.createScriptURL(value);\n                  break;\n                }\n            }\n          }\n        }\n\n        /* Handle invalid data-* attribute set by try-catching it */\n        try {\n          if (namespaceURI) {\n            currentNode.setAttributeNS(namespaceURI, name, value);\n          } else {\n            /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n            currentNode.setAttribute(name, value);\n          }\n          if (_isClobbered(currentNode)) {\n            _forceRemove(currentNode);\n          } else {\n            arrayPop(DOMPurify.removed);\n          }\n        } catch (_) {}\n      }\n\n      /* Execute a hook if present */\n      _executeHook('afterSanitizeAttributes', currentNode, null);\n    };\n\n    /**\n     * _sanitizeShadowDOM\n     *\n     * @param  {DocumentFragment} fragment to iterate over recursively\n     */\n    const _sanitizeShadowDOM = function _sanitizeShadowDOM(fragment) {\n      let shadowNode = null;\n      const shadowIterator = _createNodeIterator(fragment);\n\n      /* Execute a hook if present */\n      _executeHook('beforeSanitizeShadowDOM', fragment, null);\n      while (shadowNode = shadowIterator.nextNode()) {\n        /* Execute a hook if present */\n        _executeHook('uponSanitizeShadowNode', shadowNode, null);\n\n        /* Sanitize tags and elements */\n        if (_sanitizeElements(shadowNode)) {\n          continue;\n        }\n\n        /* Deep shadow DOM detected */\n        if (shadowNode.content instanceof DocumentFragment) {\n          _sanitizeShadowDOM(shadowNode.content);\n        }\n\n        /* Check attributes, sanitize if necessary */\n        _sanitizeAttributes(shadowNode);\n      }\n\n      /* Execute a hook if present */\n      _executeHook('afterSanitizeShadowDOM', fragment, null);\n    };\n\n    /**\n     * Sanitize\n     * Public method providing core sanitation functionality\n     *\n     * @param {String|Node} dirty string or DOM node\n     * @param {Object} cfg object\n     */\n    // eslint-disable-next-line complexity\n    DOMPurify.sanitize = function (dirty) {\n      let cfg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      let body = null;\n      let importedNode = null;\n      let currentNode = null;\n      let returnNode = null;\n      /* Make sure we have a string to sanitize.\n        DO NOT return early, as this will return the wrong type if\n        the user has requested a DOM object rather than a string */\n      IS_EMPTY_INPUT = !dirty;\n      if (IS_EMPTY_INPUT) {\n        dirty = '<!-->';\n      }\n\n      /* Stringify, in case dirty is an object */\n      if (typeof dirty !== 'string' && !_isNode(dirty)) {\n        if (typeof dirty.toString === 'function') {\n          dirty = dirty.toString();\n          if (typeof dirty !== 'string') {\n            throw typeErrorCreate('dirty is not a string, aborting');\n          }\n        } else {\n          throw typeErrorCreate('toString is not a function');\n        }\n      }\n\n      /* Return dirty HTML if DOMPurify cannot run */\n      if (!DOMPurify.isSupported) {\n        return dirty;\n      }\n\n      /* Assign config vars */\n      if (!SET_CONFIG) {\n        _parseConfig(cfg);\n      }\n\n      /* Clean up removed elements */\n      DOMPurify.removed = [];\n\n      /* Check if dirty is correctly typed for IN_PLACE */\n      if (typeof dirty === 'string') {\n        IN_PLACE = false;\n      }\n      if (IN_PLACE) {\n        /* Do some early pre-sanitization to avoid unsafe root nodes */\n        if (dirty.nodeName) {\n          const tagName = transformCaseFunc(dirty.nodeName);\n          if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n            throw typeErrorCreate('root node is forbidden and cannot be sanitized in-place');\n          }\n        }\n      } else if (dirty instanceof Node) {\n        /* If dirty is a DOM element, append to an empty document to avoid\n           elements being stripped by the parser */\n        body = _initDocument('<!---->');\n        importedNode = body.ownerDocument.importNode(dirty, true);\n        if (importedNode.nodeType === NODE_TYPE.element && importedNode.nodeName === 'BODY') {\n          /* Node is already a body, use as is */\n          body = importedNode;\n        } else if (importedNode.nodeName === 'HTML') {\n          body = importedNode;\n        } else {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          body.appendChild(importedNode);\n        }\n      } else {\n        /* Exit directly if we have nothing to do */\n        if (!RETURN_DOM && !SAFE_FOR_TEMPLATES && !WHOLE_DOCUMENT &&\n        // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1) {\n          return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(dirty) : dirty;\n        }\n\n        /* Initialize the document to work on */\n        body = _initDocument(dirty);\n\n        /* Check we have a DOM node from the data */\n        if (!body) {\n          return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n        }\n      }\n\n      /* Remove first element node (ours) if FORCE_BODY is set */\n      if (body && FORCE_BODY) {\n        _forceRemove(body.firstChild);\n      }\n\n      /* Get node iterator */\n      const nodeIterator = _createNodeIterator(IN_PLACE ? dirty : body);\n\n      /* Now start iterating over the created document */\n      while (currentNode = nodeIterator.nextNode()) {\n        /* Sanitize tags and elements */\n        if (_sanitizeElements(currentNode)) {\n          continue;\n        }\n\n        /* Shadow DOM detected, sanitize it */\n        if (currentNode.content instanceof DocumentFragment) {\n          _sanitizeShadowDOM(currentNode.content);\n        }\n\n        /* Check attributes, sanitize if necessary */\n        _sanitizeAttributes(currentNode);\n      }\n\n      /* If we sanitized `dirty` in-place, return it. */\n      if (IN_PLACE) {\n        return dirty;\n      }\n\n      /* Return sanitized string or DOM */\n      if (RETURN_DOM) {\n        if (RETURN_DOM_FRAGMENT) {\n          returnNode = createDocumentFragment.call(body.ownerDocument);\n          while (body.firstChild) {\n            // eslint-disable-next-line unicorn/prefer-dom-node-append\n            returnNode.appendChild(body.firstChild);\n          }\n        } else {\n          returnNode = body;\n        }\n        if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmode) {\n          /*\n            AdoptNode() is not used because internal state is not reset\n            (e.g. the past names map of a HTMLFormElement), this is safe\n            in theory but we would rather not risk another attack vector.\n            The state that is cloned by importNode() is explicitly defined\n            by the specs.\n          */\n          returnNode = importNode.call(originalDocument, returnNode, true);\n        }\n        return returnNode;\n      }\n      let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n      /* Serialize doctype if allowed */\n      if (WHOLE_DOCUMENT && ALLOWED_TAGS['!doctype'] && body.ownerDocument && body.ownerDocument.doctype && body.ownerDocument.doctype.name && regExpTest(DOCTYPE_NAME, body.ownerDocument.doctype.name)) {\n        serializedHTML = '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n      }\n\n      /* Sanitize final string template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], expr => {\n          serializedHTML = stringReplace(serializedHTML, expr, ' ');\n        });\n      }\n      return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(serializedHTML) : serializedHTML;\n    };\n\n    /**\n     * Public method to set the configuration once\n     * setConfig\n     *\n     * @param {Object} cfg configuration object\n     */\n    DOMPurify.setConfig = function () {\n      let cfg = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      _parseConfig(cfg);\n      SET_CONFIG = true;\n    };\n\n    /**\n     * Public method to remove the configuration\n     * clearConfig\n     *\n     */\n    DOMPurify.clearConfig = function () {\n      CONFIG = null;\n      SET_CONFIG = false;\n    };\n\n    /**\n     * Public method to check if an attribute value is valid.\n     * Uses last set config, if any. Otherwise, uses config defaults.\n     * isValidAttribute\n     *\n     * @param  {String} tag Tag name of containing element.\n     * @param  {String} attr Attribute name.\n     * @param  {String} value Attribute value.\n     * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n     */\n    DOMPurify.isValidAttribute = function (tag, attr, value) {\n      /* Initialize shared config vars if necessary. */\n      if (!CONFIG) {\n        _parseConfig({});\n      }\n      const lcTag = transformCaseFunc(tag);\n      const lcName = transformCaseFunc(attr);\n      return _isValidAttribute(lcTag, lcName, value);\n    };\n\n    /**\n     * AddHook\n     * Public method to add DOMPurify hooks\n     *\n     * @param {String} entryPoint entry point for the hook to add\n     * @param {Function} hookFunction function to execute\n     */\n    DOMPurify.addHook = function (entryPoint, hookFunction) {\n      if (typeof hookFunction !== 'function') {\n        return;\n      }\n      hooks[entryPoint] = hooks[entryPoint] || [];\n      arrayPush(hooks[entryPoint], hookFunction);\n    };\n\n    /**\n     * RemoveHook\n     * Public method to remove a DOMPurify hook at a given entryPoint\n     * (pops it from the stack of hooks if more are present)\n     *\n     * @param {String} entryPoint entry point for the hook to remove\n     * @return {Function} removed(popped) hook\n     */\n    DOMPurify.removeHook = function (entryPoint) {\n      if (hooks[entryPoint]) {\n        return arrayPop(hooks[entryPoint]);\n      }\n    };\n\n    /**\n     * RemoveHooks\n     * Public method to remove all DOMPurify hooks at a given entryPoint\n     *\n     * @param  {String} entryPoint entry point for the hooks to remove\n     */\n    DOMPurify.removeHooks = function (entryPoint) {\n      if (hooks[entryPoint]) {\n        hooks[entryPoint] = [];\n      }\n    };\n\n    /**\n     * RemoveAllHooks\n     * Public method to remove all DOMPurify hooks\n     */\n    DOMPurify.removeAllHooks = function () {\n      hooks = {};\n    };\n    return DOMPurify;\n  }\n  var purify = createDOMPurify();\n\n  return purify;\n\n}));\n//# sourceMappingURL=purify.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/dompurify@3.1.7/node_modules/dompurify/dist/purify.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/MindElixirReact.tsx":
/*!********************************************!*\
  !*** ./src/components/MindElixirReact.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var dompurify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dompurify */ \"(app-pages-browser)/./node_modules/.pnpm/dompurify@3.1.7/node_modules/dompurify/dist/purify.js\");\n/* harmony import */ var dompurify__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dompurify__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var mind_elixir__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mind-elixir */ \"(app-pages-browser)/./node_modules/.pnpm/mind-elixir@5.0.0-beta.21/node_modules/mind-elixir/dist/MindElixir.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst MindElixirReact = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { data, options, plugins, initScale, className } = param;\n    _s();\n    const mindmapEl = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const meInstance = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            instance: meInstance.current\n        }));\n    const sanitizeNodeData = (nodeData)=>{\n        if (!nodeData) return;\n        if (nodeData.dangerouslySetInnerHTML) {\n            nodeData.dangerouslySetInnerHTML = dompurify__WEBPACK_IMPORTED_MODULE_2___default().sanitize(nodeData.dangerouslySetInnerHTML);\n        }\n        if (nodeData.children) {\n            for (const child of nodeData.children){\n                sanitizeNodeData(child);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mindmapEl.current || \"object\" === \"undefined\") return;\n        const mediaQuery = window.matchMedia(\"(prefers-color-scheme: dark)\");\n        const changeTheme = (e)=>{\n            if (e.matches) {\n                var _meInstance_current;\n                (_meInstance_current = meInstance.current) === null || _meInstance_current === void 0 ? void 0 : _meInstance_current.changeTheme(mind_elixir__WEBPACK_IMPORTED_MODULE_3__[\"default\"].DARK_THEME);\n            } else {\n                var _meInstance_current1;\n                (_meInstance_current1 = meInstance.current) === null || _meInstance_current1 === void 0 ? void 0 : _meInstance_current1.changeTheme(mind_elixir__WEBPACK_IMPORTED_MODULE_3__[\"default\"].THEME);\n            }\n        };\n        const mergedOptions = {\n            ...options,\n            el: mindmapEl.current\n        };\n        meInstance.current = new mind_elixir__WEBPACK_IMPORTED_MODULE_3__[\"default\"](mergedOptions);\n        // Install plugins\n        if (plugins) {\n            for (const plugin of plugins){\n                meInstance.current.install(plugin);\n            }\n        }\n        // Set initial scale\n        if (initScale) {\n            meInstance.current.scaleVal = initScale;\n            meInstance.current.map.style.transform = \"scale(\".concat(initScale, \")\");\n        }\n        meInstance.current.map.style.opacity = \"0\";\n        mediaQuery.addEventListener(\"change\", changeTheme);\n        return ()=>{\n            mediaQuery.removeEventListener(\"change\", changeTheme);\n        };\n    }, [\n        options,\n        plugins,\n        initScale\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!data || !meInstance.current) return;\n        sanitizeNodeData(data.nodeData);\n        meInstance.current.init(data);\n        meInstance.current.toCenter();\n        meInstance.current.map.style.opacity = \"1\";\n    }, [\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: mindmapEl,\n        className: className\n    }, void 0, false, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindElixirReact.tsx\",\n        lineNumber: 93,\n        columnNumber: 12\n    }, undefined);\n}, \"tk/LufcUWzEIz5Ax0QD2xWd/TwI=\")), \"tk/LufcUWzEIz5Ax0QD2xWd/TwI=\");\n_c1 = MindElixirReact;\nMindElixirReact.displayName = \"MindElixirReact\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (MindElixirReact);\nvar _c, _c1;\n$RefreshReg$(_c, \"MindElixirReact$forwardRef\");\n$RefreshReg$(_c1, \"MindElixirReact\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindElixirReact.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/mind-elixir@5.0.0-beta.21/node_modules/mind-elixir/dist/MindElixir.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/mind-elixir@5.0.0-beta.21/node_modules/mind-elixir/dist/MindElixir.js ***!
  \**************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ j; }\n/* harmony export */ });\n(function(){\"use strict\";try{if(typeof document<\"u\"){var e=document.createElement(\"style\");e.appendChild(document.createTextNode(\".map-container{--gap: 30px;--root-radius: 30px;--main-radius: 20px;--root-color: #ffffff;--root-bgcolor: #4c4f69;--root-border-color: rgba(0, 0, 0, 0);--main-color: #444446;--main-bgcolor: #ffffff;--topic-padding: 3px;--color: #777777;--bgcolor: #f6f6f6;--selected: #4dc4ff;--panel-color: #444446;--panel-bgcolor: #ffffff;--panel-border-color: #eaeaea;-webkit-tap-highlight-color:rgba(0,0,0,0);font-family:-apple-system,BlinkMacSystemFont,Helvetica Neue,PingFang SC,Microsoft YaHei,Source Han Sans SC,Noto Sans CJK SC,WenQuanYi Micro Hei,sans-serif;-webkit-user-select:none;user-select:none;height:100%;width:100%;overflow:hidden;font-size:15px}.map-container *{box-sizing:border-box}.map-container::-webkit-scrollbar{width:0px;height:0px}.map-container .selected{outline:2px solid var(--selected);outline-offset:1px}.map-container .hyper-link{text-decoration:none;margin-left:.3em}.map-container .lhs{direction:rtl}.map-container .lhs me-tpc{direction:ltr}.map-container .map-canvas{height:20000px;width:20000px;position:relative;-webkit-user-select:none;user-select:none;transform:scale(1);background-color:var(--bgcolor)}.map-container .map-canvas me-nodes{position:absolute;display:flex;justify-content:center;align-items:center;height:fit-content;width:fit-content}.map-container .map-canvas me-root{position:relative}.map-container .map-canvas me-root me-tpc{display:block;font-size:25px;color:var(--root-color);padding:10px var(--gap);border-radius:var(--root-radius);border:var(--root-border-color) 2px solid;white-space:pre-wrap;background-color:var(--root-bgcolor)}.map-container me-main>me-wrapper{position:relative;margin:45px 65px}.map-container me-main>me-wrapper>me-parent{margin:10px;padding:0}.map-container me-main>me-wrapper>me-parent>me-tpc{border-radius:var(--main-radius);background-color:var(--main-bgcolor);border:2px solid var(--main-color);color:var(--main-color);padding:8px 25px}.map-container me-wrapper{display:block;pointer-events:none;width:fit-content}.map-container me-children,.map-container me-parent{display:inline-block;vertical-align:middle}.map-container me-parent{position:relative;cursor:pointer;padding:6px var(--gap);margin-top:10px}.map-container me-parent me-tpc{position:relative;display:block;border-radius:3px;color:var(--color);pointer-events:all;max-width:35em;white-space:pre-wrap;padding:var(--topic-padding)}.map-container me-parent me-tpc .insert-preview{position:absolute;width:100%;left:0;z-index:9}.map-container me-parent me-tpc .show{background:#7ad5ff;pointer-events:none;opacity:.7;border-radius:3px}.map-container me-parent me-tpc .before{height:14px;top:-14px}.map-container me-parent me-tpc .in{height:100%;top:0}.map-container me-parent me-tpc .after{height:14px;bottom:-14px}.map-container me-parent me-epd{position:absolute;height:18px;width:18px;opacity:.8;background-image:url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+DQo8c3ZnIHQ9IjE2NTY2NTQ3MTcyNDIiIGNsYXNzPSJpY29uIiB2aWV3Qm94PSIwIDAgMTAyNCAxMDI0IiB2ZXJzaW9uPSIxLjEiDQogICAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIg0KICAgIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+DQogICAgPHBhdGggZD0iTTUxMiA3NC42NjY2NjdDMjcwLjkzMzMzMyA3NC42NjY2NjcgNzQuNjY2NjY3IDI3MC45MzMzMzMgNzQuNjY2NjY3IDUxMlMyNzAuOTMzMzMzIDk0OS4zMzMzMzMgNTEyIDk0OS4zMzMzMzMgOTQ5LjMzMzMzMyA3NTMuMDY2NjY3IDk0OS4zMzMzMzMgNTEyIDc1My4wNjY2NjcgNzQuNjY2NjY3IDUxMiA3NC42NjY2Njd6IiBzdHJva2Utd2lkdGg9IjU0IiBzdHJva2U9J2JsYWNrJyBmaWxsPSd3aGl0ZScgPjwvcGF0aD4NCiAgICA8cGF0aCBkPSJNNjgyLjY2NjY2NyA0ODBoLTEzOC42NjY2NjdWMzQxLjMzMzMzM2MwLTE3LjA2NjY2Ny0xNC45MzMzMzMtMzItMzItMzJzLTMyIDE0LjkzMzMzMy0zMiAzMnYxMzguNjY2NjY3SDM0MS4zMzMzMzNjLTE3LjA2NjY2NyAwLTMyIDE0LjkzMzMzMy0zMiAzMnMxNC45MzMzMzMgMzIgMzIgMzJoMTM4LjY2NjY2N1Y2ODIuNjY2NjY3YzAgMTcuMDY2NjY3IDE0LjkzMzMzMyAzMiAzMiAzMnMzMi0xNC45MzMzMzMgMzItMzJ2LTEzOC42NjY2NjdINjgyLjY2NjY2N2MxNy4wNjY2NjcgMCAzMi0xNC45MzMzMzMgMzItMzJzLTE0LjkzMzMzMy0zMi0zMi0zMnoiPjwvcGF0aD4NCjwvc3ZnPg==);background-repeat:no-repeat;background-size:contain;background-position:center;pointer-events:all;z-index:9}.map-container me-parent me-epd.minus{background-image:url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+DQo8c3ZnIHQ9IjE2NTY2NTU1NjQ5ODUiIGNsYXNzPSJpY29uIiB2aWV3Qm94PSIwIDAgMTAyNCAxMDI0IiB2ZXJzaW9uPSIxLjEiDQogICAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIg0KICAgIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+DQogICAgPHBhdGggZD0iTTUxMiA3NC42NjY2NjdDMjcwLjkzMzMzMyA3NC42NjY2NjcgNzQuNjY2NjY3IDI3MC45MzMzMzMgNzQuNjY2NjY3IDUxMlMyNzAuOTMzMzMzIDk0OS4zMzMzMzMgNTEyIDk0OS4zMzMzMzMgOTQ5LjMzMzMzMyA3NTMuMDY2NjY3IDk0OS4zMzMzMzMgNTEyIDc1My4wNjY2NjcgNzQuNjY2NjY3IDUxMiA3NC42NjY2Njd6IiBzdHJva2Utd2lkdGg9IjU0IiBzdHJva2U9J2JsYWNrJyBmaWxsPSd3aGl0ZScgPjwvcGF0aD4NCiAgICA8cGF0aCBkPSJNNjgyLjY2NjY2NyA1NDRIMzQxLjMzMzMzM2MtMTcuMDY2NjY3IDAtMzItMTQuOTMzMzMzLTMyLTMyczE0LjkzMzMzMy0zMiAzMi0zMmgzNDEuMzMzMzM0YzE3LjA2NjY2NyAwIDMyIDE0LjkzMzMzMyAzMiAzMnMtMTQuOTMzMzMzIDMyLTMyIDMyeiI+PC9wYXRoPg0KPC9zdmc+)!important;transition:opacity .3s;opacity:0}.map-container me-parent me-epd.minus:hover{opacity:.8}.map-container .icon{width:1em;height:1em;vertical-align:-.15em;fill:currentColor;overflow:hidden}.map-container .lines,.map-container .summary,.map-container .subLines,.map-container .topiclinks,.map-container .linkcontroller{position:absolute;height:102%;width:100%;top:0;left:0}.map-container .topiclinks,.map-container .linkcontroller,.map-container .summary,.map-container .topiclinks .selected,.map-container .linkcontroller .selected,.map-container .summary .selected{pointer-events:none}.map-container .summary>g,.map-container .topiclinks>g{cursor:pointer;pointer-events:stroke}.map-container .lines,.map-container .subLines{pointer-events:none;z-index:-1}.map-container .topiclinks *,.map-container .linkcontroller *{z-index:100}.map-container #input-box{position:absolute;top:0;left:0;width:max-content;max-width:35em;z-index:11;direction:ltr;-webkit-user-select:auto;user-select:auto;pointer-events:auto;color:var(--color);background-color:var(--bgcolor)}.map-container me-tpc>*{pointer-events:none}.map-container me-tpc>a,.map-container me-tpc>iframe{pointer-events:auto}.map-container me-tpc>img{display:block;margin-bottom:8px;object-fit:cover}.map-container me-tpc>.text{display:inline-block}.map-container .circle{position:absolute;height:10px;width:10px;margin-top:-5px;margin-left:-5px;border-radius:100%;background:#757575;border:2px solid #ffffff;cursor:pointer}.map-container .tags{direction:ltr}.map-container .tags span{display:inline-block;border-radius:3px;padding:2px 4px;background:#d6f0f8;color:#276f86;margin:2px 4px 0 0;font-size:12px;line-height:1.3em}.map-container .icons{display:inline-block;direction:ltr;margin-left:5px}.map-container .icons span{display:inline-block;line-height:1.3em}.map-container .mind-elixir-ghost{position:fixed;top:-100%;left:-100%;box-sizing:content-box;opacity:.5;background-color:var(--main-bgcolor);border:2px solid var(--main-color);color:var(--main-color);max-width:200px;width:fit-content;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;padding:8px 16px;border-radius:6px}.map-container .selection-area{background:#4f90f22d;border:1px solid #4f90f2}.map-container .context-menu{position:fixed;top:0;left:0;width:100%;height:100%;z-index:99}.map-container .context-menu .menu-list{position:fixed;list-style:none;margin:0;padding:0;color:var(--panel-color);box-shadow:0 12px 15px #0003;border-radius:5px;overflow:hidden}.map-container .context-menu .menu-list li{min-width:200px;overflow:hidden;white-space:nowrap;padding:6px 10px;background:var(--panel-bgcolor);border-bottom:1px solid var(--panel-border-color);cursor:pointer}.map-container .context-menu .menu-list li span{line-height:20px}.map-container .context-menu .menu-list li a{color:#333;text-decoration:none}.map-container .context-menu .menu-list li.disabled{display:none}.map-container .context-menu .menu-list li:hover{filter:brightness(.95)}.map-container .context-menu .menu-list li:last-child{border-bottom:0}.map-container .context-menu .menu-list li span:last-child{float:right}.map-container .context-menu .key{font-size:10px;background-color:#f1f1f1;color:#333;padding:2px 5px;border-radius:3px}.map-container .tips{position:absolute;bottom:20px;left:50%;transform:translate(-50%);color:var(--panel-color);font-weight:bolder}.mind-elixir-toolbar{font-family:iconfont;position:absolute;color:var(--panel-color);background:var(--panel-bgcolor);padding:10px;border-radius:5px;box-shadow:0 1px 2px #0003}.mind-elixir-toolbar svg{display:inline-block}.mind-elixir-toolbar span:active{opacity:.5}.mind-elixir-toolbar.rb{right:20px;bottom:20px}.mind-elixir-toolbar.rb span+span{margin-left:10px}.mind-elixir-toolbar.lt{font-size:20px;left:20px;top:20px}.mind-elixir-toolbar.lt span{display:block}.mind-elixir-toolbar.lt span+span{margin-top:10px}\")),document.head.appendChild(e)}}catch(o){console.error(\"vite-plugin-css-injected-by-js\",o)}})();\nvar nt = Object.defineProperty;\nvar ot = (e, t, n) => t in e ? nt(e, t, { enumerable: !0, configurable: !0, writable: !0, value: n }) : e[t] = n;\nvar G = (e, t, n) => (ot(e, typeof t != \"symbol\" ? t + \"\" : t, n), n);\n(function(e) {\n  var t, n, o, s, i, r, c = '<svg><symbol id=\"icon-edit\" viewBox=\"0 0 1024 1024\"><path d=\"M423.765333 128a42.666667 42.666667 0 0 1 3.2 85.205333L423.765333 213.333333H234.666667a64 64 0 0 0-63.872 60.245334L170.666667 277.333333v512a64 64 0 0 0 60.245333 63.872L234.666667 853.333333h512a64 64 0 0 0 63.872-60.245333L810.666667 789.333333v-189.098666a42.666667 42.666667 0 0 1 85.205333-3.2l0.128 3.2V789.333333a149.333333 149.333333 0 0 1-144.213333 149.248L746.666667 938.666667h-512a149.333333 149.333333 0 0 1-149.248-144.213334L85.333333 789.333333v-512a149.333333 149.333333 0 0 1 144.213334-149.248L234.666667 128h189.098666z m324.949334-53.248a42.666667 42.666667 0 0 1 60.330666 0l150.869334 150.869333a42.666667 42.666667 0 0 1 0 60.330667l-329.386667 329.386667a42.666667 42.666667 0 0 1-29.44 12.458666l-153.386667 2.517334a42.666667 42.666667 0 0 1-43.349333-43.349334l2.56-153.386666a42.666667 42.666667 0 0 1 12.458667-29.44z m30.165333 90.496L491.946667 452.266667l-1.493334 91.989333 92.032-1.493333 286.976-286.976-90.538666-90.538667z\"  ></path></symbol><symbol id=\"icon-rising\" viewBox=\"0 0 1024 1024\"><path d=\"M553.173333 803.84h-64l0.021334-474.581333-224.021334 224-45.269333-45.226667L521.6 206.293333l301.717333 301.696-45.269333 45.269334-224.853333-224.896v475.477333z\"  ></path></symbol><symbol id=\"icon-falling\" viewBox=\"0 0 1024 1024\"><path d=\"M553.173333 238.314667h-64l0.021334 474.602666-224.021334-224-45.269333 45.226667L521.6 835.861333l301.717333-301.717333-45.269333-45.226667-224.853333 224.853334V238.336z\"  ></path></symbol><symbol id=\"icon-shanchu2\" viewBox=\"0 0 1024 1024\"><path d=\"M516.60601807 107.93026734c-82.64382935 0-149.71865844 65.51751709-152.5729065 147.77160644H171.37136841c-21.40603638 0-38.92044068 17.38504028-38.92044068 38.92126465 0 21.40686036 17.38504028 38.92208862 38.92126466 38.92208862h42.94308471v435.40136719c0 81.73498536 55.39828492 148.55026245 123.90106201 148.55026245h348.99444581c68.37341309 0 123.90106201-66.42553711 123.901062-148.55026245V333.80477906h38.92126465c21.40686036 0 38.92126464-17.38586426 38.92126465-38.92208863 0-21.40686036-17.38504028-38.92126464-38.92126465-38.92126465H668.91854859C666.45321656 173.44860839 599.24902344 107.93109131 516.60601807 107.93109131z m-79.65939331 147.77160644c2.85424805-42.16442872 37.2354126-74.85809326 79.78875732-74.85809326s76.93450927 32.82302857 79.39984131 74.85809326H436.94662476z m-98.86047364 589.01165771c-24.2611084 0-50.98754883-31.13717651-50.98754883-75.76693725V333.80477906h450.97036744V769.33551026c0 44.50039673-26.72644043 75.76776123-50.98754884 75.76776122H338.08615112v-0.38973999z m0 0\"  ></path><path d=\"M390.37063599 751.17263794c17.77313232 0 32.43411255-17.7739563 32.43411255-40.08883667V482.35504151c0-22.31488037-14.53079224-40.08966065-32.43411255-40.08966065-17.77478027 0-32.43493653 17.77478027-32.43493653 40.08966065v228.72875976c0 22.18469239 14.27124023 40.08883667 32.43493653 40.08883667z m117.41308594 0c17.7739563 0 32.43411255-17.7739563 32.43411255-40.08883667V482.35504151c0-22.31488037-14.53079224-40.08966065-32.43411255-40.08966065-17.7739563 0-32.43493653 17.77478027-32.43493653 40.08966065v228.72875976c0 22.18469239 14.66098023 40.08883667 32.43493653 40.08883667z m123.51049804 0c17.7739563 0 32.43493653-17.7739563 32.43493652-40.08883667V482.35504151c0-22.31488037-14.53079224-40.08966065-32.43493652-40.08966065-17.7739563 0-32.43411255 17.77478027-32.43411255 40.08966065v228.72875976c0 22.18469239 14.14105224 40.08883667 32.43411255 40.08883667z m0 0\"  ></path></symbol><symbol id=\"icon-zijiedian\" viewBox=\"0 0 1024 1024\"><path d=\"M312.208 472c19.568-157.856 153.432-280 315.656-280 175.68 0 318.112 143.272 318.112 320S803.552 832 627.864 832c-162.224 0-296.08-122.144-315.656-280H120a40 40 0 0 1 0-80h192.208zM632 752c132.552 0 240-107.448 240-240 0-132.552-107.448-240-240-240-132.552 0-240 107.448-240 240 0 132.552 107.448 240 240 240z m-40-280v-80a40 40 0 0 1 80 0v80h80a40 40 0 0 1 0 80h-80v80a40 40 0 0 1-80 0v-80h-80a40 40 0 0 1 0-80h80z\"  ></path></symbol><symbol id=\"icon-tongjijiedian-\" viewBox=\"0 0 1024 1024\"><path d=\"M803.84 131.626667H410.24A59.733333 59.733333 0 0 0 350.506667 192v45.226667H199.68a51.626667 51.626667 0 0 0-51.626667 51.626666v465.92a51.626667 51.626667 0 0 0 51.626667 51.626667h187.52v-55.466667h-162.133333a21.333333 21.333333 0 0 1-21.333334-21.333333V313.386667a21.333333 21.333333 0 0 1 21.333334-21.333334h125.653333v64a59.733333 59.733333 0 0 0 59.733333 59.733334h393.386667a59.733333 59.733333 0 0 0 59.733333-59.733334V192a59.733333 59.733333 0 0 0-59.733333-60.373333z m4.266667 224.64a4.266667 4.266667 0 0 1-4.266667 4.266666H410.24a4.266667 4.266667 0 0 1-4.266667-4.266666V192a4.266667 4.266667 0 0 1 4.266667-4.266667h393.6a4.266667 4.266667 0 0 1 4.266667 4.266667zM716.16 749.44h-81.28v-81.493333a27.733333 27.733333 0 0 0-55.466667 0v81.28h-81.493333a27.733333 27.733333 0 1 0 0 55.466666h81.28v81.28a27.733333 27.733333 0 1 0 55.466667 0v-81.066666h81.28a27.733333 27.733333 0 0 0 0-55.466667z\"  ></path></symbol><symbol id=\"icon-close\" viewBox=\"0 0 1024 1024\"><path d=\"M557.312 513.248l265.28-263.904c12.544-12.48 12.608-32.704 0.128-45.248-12.512-12.576-32.704-12.608-45.248-0.128L512.128 467.904l-263.04-263.84c-12.448-12.48-32.704-12.544-45.248-0.064-12.512 12.48-12.544 32.736-0.064 45.28l262.976 263.776L201.6 776.8c-12.544 12.48-12.608 32.704-0.128 45.248a31.937 31.937 0 0 0 22.688 9.44c8.16 0 16.32-3.104 22.56-9.312l265.216-263.808 265.44 266.24c6.24 6.272 14.432 9.408 22.656 9.408a31.94 31.94 0 0 0 22.592-9.344c12.512-12.48 12.544-32.704 0.064-45.248L557.312 513.248z\" fill=\"\" ></path></symbol><symbol id=\"icon-menu\" viewBox=\"0 0 1024 1024\"><path d=\"M109.714 292.571h804.572c21.943 0 36.571-21.942 36.571-43.885 0-14.629-14.628-29.257-36.571-29.257H109.714c-21.943 0-36.571 14.628-36.571 36.571 0 14.629 14.628 36.571 36.571 36.571zM914.286 512H109.714c-21.943 0-36.571 14.629-36.571 36.571 0 14.629 14.628 36.572 36.571 36.572h804.572c21.943 0 36.571-21.943 36.571-43.886 0-14.628-14.628-29.257-36.571-29.257z m0 292.571H109.714c-21.943 0-36.571 14.629-36.571 36.572s14.628 36.571 36.571 36.571h804.572c21.943 0 36.571-21.943 36.571-36.571 0-21.943-14.628-36.572-36.571-36.572z\"  ></path></symbol><symbol id=\"icon-right\" viewBox=\"0 0 1024 1024\"><path d=\"M385 560.69999999L385 738.9c0 36.90000001 26.4 68.5 61.3 68.5l150.2 0c1.5 0 3-0.1 4.5-0.3 10.2 38.7 45.5 67.3 87.5 67.3 50 0 90.5-40.5 90.5-90.5s-40.5-90.5-90.5-90.5c-42 0-77.3 28.6-87.5 67.39999999-1.4-0.3-2.9-0.4-4.5-0.39999999L446.3 760.4c-6.8 0-14.3-8.9-14.3-21.49999999l0-427.00000001c0-12.7 7.40000001-21.5 14.30000001-21.5l150.19999999 0c1.5 0 3-0.2 4.5-0.4 10.2 38.8 45.5 67.3 87.5 67.3 50 0 90.5-40.5 90.5-90.4 0-49.9-40.5-90.6-90.5-90.59999999-42 0-77.3 28.6-87.5 67.39999999-1.4-0.2-2.9-0.4-4.49999999-0.4L446.3 243.3c-34.80000001 0-61.3 31.6-61.3 68.50000001L385 513.7l-79.1 0c-10.4-38.5-45.49999999-67-87.4-67-50 0-90.5 40.5-90.5 90.5s40.5 90.5 90.5 90.5c41.79999999 0 77.00000001-28.4 87.4-67L385 560.69999999z\" fill=\"\" ></path></symbol><symbol id=\"icon-left\" viewBox=\"0 0 1024 1024\"><path d=\"M639 463.30000001L639 285.1c0-36.90000001-26.4-68.5-61.3-68.5l-150.2 0c-1.5 0-3 0.1-4.5 0.3-10.2-38.7-45.5-67.3-87.5-67.3-50 0-90.5 40.5-90.5 90.5s40.5 90.5 90.5 90.5c42 0 77.3-28.6 87.5-67.39999999 1.4 0.3 2.9 0.4 4.5 0.39999999L577.7 263.6c6.8 0 14.3 8.9 14.3 21.49999999l0 427.00000001c0 12.7-7.40000001 21.5-14.30000001 21.5l-150.19999999 0c-1.5 0-3 0.2-4.5 0.4-10.2-38.8-45.5-67.3-87.5-67.3-50 0-90.5 40.5-90.5 90.4 0 49.9 40.5 90.6 90.5 90.59999999 42 0 77.3-28.6 87.5-67.39999999 1.4 0.2 2.9 0.4 4.49999999 0.4L577.7 780.7c34.80000001 0 61.3-31.6 61.3-68.50000001L639 510.3l79.1 0c10.4 38.5 45.49999999 67 87.4 67 50 0 90.5-40.5 90.5-90.5s-40.5-90.5-90.5-90.5c-41.79999999 0-77.00000001 28.4-87.4 67L639 463.30000001z\" fill=\"\" ></path></symbol><symbol id=\"icon-side\" viewBox=\"0 0 1024 1024\"><path d=\"M851.91168 328.45312c-59.97056 0-108.6208 48.47104-108.91264 108.36992l-137.92768 38.4a109.14304 109.14304 0 0 0-63.46752-46.58688l1.39264-137.11872c47.29344-11.86816 82.31936-54.66624 82.31936-105.64096 0-60.15488-48.76288-108.91776-108.91776-108.91776s-108.91776 48.76288-108.91776 108.91776c0 49.18784 32.60928 90.75712 77.38368 104.27392l-1.41312 138.87488a109.19936 109.19936 0 0 0-63.50336 48.55808l-138.93632-39.48544 0.01024-0.72704c0-60.15488-48.76288-108.91776-108.91776-108.91776s-108.91776 48.75776-108.91776 108.91776c0 60.15488 48.76288 108.91264 108.91776 108.91264 39.3984 0 73.91232-20.92032 93.03552-52.2496l139.19232 39.552-0.00512 0.2304c0 25.8304 9.00096 49.5616 24.02816 68.23424l-90.14272 132.63872a108.7488 108.7488 0 0 0-34.2528-5.504c-60.15488 0-108.91776 48.768-108.91776 108.91776 0 60.16 48.76288 108.91776 108.91776 108.91776 60.16 0 108.92288-48.75776 108.92288-108.91776 0-27.14624-9.9328-51.968-26.36288-71.04l89.04704-131.03104a108.544 108.544 0 0 0 37.6832 6.70208 108.672 108.672 0 0 0 36.48512-6.272l93.13792 132.57216a108.48256 108.48256 0 0 0-24.69888 69.0688c0 60.16 48.768 108.92288 108.91776 108.92288 60.16 0 108.91776-48.76288 108.91776-108.92288 0-60.14976-48.75776-108.91776-108.91776-108.91776a108.80512 108.80512 0 0 0-36.69504 6.3488l-93.07136-132.48a108.48768 108.48768 0 0 0 24.79616-72.22784l136.09984-37.888c18.99008 31.93856 53.84192 53.3504 93.69088 53.3504 60.16 0 108.92288-48.75776 108.92288-108.91264-0.00512-60.15488-48.77312-108.92288-108.92288-108.92288z\"  ></path></symbol><symbol id=\"icon-B\" viewBox=\"0 0 1024 1024\"><path d=\"M98.067692 65.457231H481.28c75.854769 0 132.411077 3.150769 169.668923 9.452307 37.336615 6.301538 70.656 19.534769 100.036923 39.620924 29.459692 20.007385 53.956923 46.710154 73.570462 80.029538 19.692308 33.398154 29.459692 70.734769 29.459692 112.167385 0 44.898462-12.130462 86.094769-36.233846 123.588923a224.886154 224.886154 0 0 1-98.461539 84.283077c58.368 17.092923 103.266462 46.08 134.695385 87.04 31.350154 40.96 47.025231 89.088 47.025231 144.462769 0 43.638154-10.082462 86.016-30.404923 127.212308-20.243692 41.196308-47.891692 74.043077-83.02277 98.697846-35.052308 24.654769-78.296615 39.778462-129.732923 45.449846-32.295385 3.465846-110.119385 5.671385-233.472 6.537846H98.067692V65.457231z m193.536 159.507692V446.621538h126.818462c75.460923 0 122.328615-1.024 140.603077-3.229538 33.083077-3.938462 59.155692-15.36 78.139077-34.343385 18.904615-18.904615 28.435692-43.874462 28.435692-74.830769 0-29.696-8.192-53.720615-24.497231-72.310154-16.384-18.510769-40.644923-29.696-72.940307-33.634461-19.140923-2.205538-74.279385-3.308308-165.415385-3.308308h-111.064615z m0 381.243077v256.315077h179.2c69.710769 0 113.979077-1.969231 132.726154-5.907692 28.750769-5.198769 52.145231-17.959385 70.262154-38.281847 18.116923-20.243692 27.096615-47.340308 27.096615-81.368615 0-28.750769-6.931692-53.169231-20.873846-73.255385a118.232615 118.232615 0 0 0-60.494769-43.795692c-26.387692-9.137231-83.574154-13.705846-171.638154-13.705846H291.603692z\"  ></path></symbol><symbol id=\"icon-a\" viewBox=\"0 0 1024 1024\"><path d=\"M757.76 665.6q0 20.48 1.536 34.304t7.68 22.016 18.944 12.288 34.304 4.096q-3.072 25.6-15.36 44.032-11.264 16.384-33.28 29.696t-62.976 13.312q-11.264 0-20.48-0.512t-17.408-2.56l-6.144-2.048-1.024 0q-4.096-1.024-10.24-4.096-2.048-2.048-4.096-2.048-1.024-1.024-2.048-1.024-14.336-8.192-23.552-17.408t-14.336-17.408q-6.144-10.24-9.216-20.48-63.488 75.776-178.176 75.776-48.128 0-88.064-15.36t-69.12-44.032-45.056-68.096-15.872-88.576 16.896-89.088 47.616-67.584 74.24-42.496 96.768-14.848q48.128 0 88.576 17.408t66.048 49.152q0-8.192 0.512-16.384t0.512-15.36q0-71.68-39.936-104.448t-128-32.768q-43.008 0-84.992 6.656t-84.992 17.92q14.336-28.672 25.088-47.616t24.064-29.184q30.72-24.576 158.72-24.576 79.872 0 135.168 13.824t90.624 43.52 51.2 75.264 15.872 108.032l0 200.704zM487.424 743.424q50.176 0 79.872-33.28t29.696-95.744q0-61.44-28.672-93.696t-76.8-32.256q-52.224 0-82.944 33.28t-30.72 94.72q0 58.368 31.744 92.672t77.824 34.304z\"  ></path></symbol><symbol id=\"icon-full\" viewBox=\"0 0 1024 1024\"><path d=\"M639.328 416c8.032 0 16.096-3.008 22.304-9.056l202.624-197.184-0.8 143.808c-0.096 17.696 14.144 32.096 31.808 32.192 0.064 0 0.128 0 0.192 0 17.6 0 31.904-14.208 32-31.808l1.248-222.208c0-0.672-0.352-1.248-0.384-1.92 0.032-0.512 0.288-0.896 0.288-1.408 0.032-17.664-14.272-32-31.968-32.032L671.552 96l-0.032 0c-17.664 0-31.968 14.304-32 31.968C639.488 145.632 653.824 160 671.488 160l151.872 0.224-206.368 200.8c-12.672 12.32-12.928 32.608-0.64 45.248C622.656 412.736 630.976 416 639.328 416z\"  ></path><path d=\"M896.032 639.552 896.032 639.552c-17.696 0-32 14.304-32.032 31.968l-0.224 151.872-200.832-206.4c-12.32-12.64-32.576-12.96-45.248-0.64-12.672 12.352-12.928 32.608-0.64 45.248l197.184 202.624-143.808-0.8c-0.064 0-0.128 0-0.192 0-17.6 0-31.904 14.208-32 31.808-0.096 17.696 14.144 32.096 31.808 32.192l222.24 1.248c0.064 0 0.128 0 0.192 0 0.64 0 1.12-0.32 1.76-0.352 0.512 0.032 0.896 0.288 1.408 0.288l0.032 0c17.664 0 31.968-14.304 32-31.968L928 671.584C928.032 653.952 913.728 639.584 896.032 639.552z\"  ></path><path d=\"M209.76 159.744l143.808 0.8c0.064 0 0.128 0 0.192 0 17.6 0 31.904-14.208 32-31.808 0.096-17.696-14.144-32.096-31.808-32.192L131.68 95.328c-0.064 0-0.128 0-0.192 0-0.672 0-1.248 0.352-1.888 0.384-0.448 0-0.8-0.256-1.248-0.256 0 0-0.032 0-0.032 0-17.664 0-31.968 14.304-32 31.968L96 352.448c-0.032 17.664 14.272 32 31.968 32.032 0 0 0.032 0 0.032 0 17.664 0 31.968-14.304 32-31.968l0.224-151.936 200.832 206.4c6.272 6.464 14.624 9.696 22.944 9.696 8.032 0 16.096-3.008 22.304-9.056 12.672-12.32 12.96-32.608 0.64-45.248L209.76 159.744z\"  ></path><path d=\"M362.368 617.056l-202.624 197.184 0.8-143.808c0.096-17.696-14.144-32.096-31.808-32.192-0.064 0-0.128 0-0.192 0-17.6 0-31.904 14.208-32 31.808l-1.248 222.24c0 0.704 0.352 1.312 0.384 2.016 0 0.448-0.256 0.832-0.256 1.312-0.032 17.664 14.272 32 31.968 32.032L352.448 928c0 0 0.032 0 0.032 0 17.664 0 31.968-14.304 32-31.968s-14.272-32-31.968-32.032l-151.936-0.224 206.4-200.832c12.672-12.352 12.96-32.608 0.64-45.248S375.008 604.704 362.368 617.056z\"  ></path></symbol><symbol id=\"icon-add\" viewBox=\"0 0 1024 1024\"><path d=\"M863.328 482.56l-317.344-1.12L545.984 162.816c0-17.664-14.336-32-32-32s-32 14.336-32 32l0 318.4L159.616 480.064c-0.032 0-0.064 0-0.096 0-17.632 0-31.936 14.24-32 31.904C127.424 529.632 141.728 544 159.392 544.064l322.592 1.152 0 319.168c0 17.696 14.336 32 32 32s32-14.304 32-32l0-318.944 317.088 1.12c0.064 0 0.096 0 0.128 0 17.632 0 31.936-14.24 32-31.904C895.264 496.992 880.96 482.624 863.328 482.56z\"  ></path></symbol><symbol id=\"icon-move\" viewBox=\"0 0 1024 1024\"><path d=\"M863.744 544 163.424 544c-17.664 0-32-14.336-32-32s14.336-32 32-32l700.32 0c17.696 0 32 14.336 32 32S881.44 544 863.744 544z\"  ></path></symbol><symbol id=\"icon-living\" viewBox=\"0 0 1024 1024\"><path d=\"M514.133333 488.533333m-106.666666 0a106.666667 106.666667 0 1 0 213.333333 0 106.666667 106.666667 0 1 0-213.333333 0Z\" fill=\"\" ></path><path d=\"M512 64C264.533333 64 64 264.533333 64 512c0 236.8 183.466667 428.8 416 445.866667v-134.4c-53.333333-59.733333-200.533333-230.4-200.533333-334.933334 0-130.133333 104.533333-234.666667 234.666666-234.666666s234.666667 104.533333 234.666667 234.666666c0 61.866667-49.066667 153.6-145.066667 270.933334l-59.733333 68.266666V960C776.533333 942.933333 960 748.8 960 512c0-247.466667-200.533333-448-448-448z\" fill=\"\" ></path></symbol></svg>', a = (a = document.getElementsByTagName(\"script\"))[a.length - 1].getAttribute(\"data-injectcss\");\n  if (a && !e.__iconfont__svg__cssinject__) {\n    e.__iconfont__svg__cssinject__ = !0;\n    try {\n      document.write(\n        \"<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>\"\n      );\n    } catch {\n    }\n  }\n  function d() {\n    i || (i = !0, o());\n  }\n  t = function() {\n    var l, u, h, v;\n    (v = document.createElement(\"div\")).innerHTML = c, c = null, (h = v.getElementsByTagName(\"svg\")[0]) && (h.setAttribute(\"aria-hidden\", \"true\"), h.style.position = \"absolute\", h.style.width = 0, h.style.height = 0, h.style.overflow = \"hidden\", l = h, (u = document.body).firstChild ? (v = l, (h = u.firstChild).parentNode.insertBefore(v, h)) : u.appendChild(l));\n  }, document.addEventListener ? ~[\"complete\", \"loaded\", \"interactive\"].indexOf(document.readyState) ? setTimeout(t, 0) : (n = function() {\n    document.removeEventListener(\"DOMContentLoaded\", n, !1), t();\n  }, document.addEventListener(\"DOMContentLoaded\", n, !1)) : document.attachEvent && (o = t, s = e.document, i = !1, (r = function() {\n    try {\n      s.documentElement.doScroll(\"left\");\n    } catch {\n      return void setTimeout(r, 50);\n    }\n    d();\n  })(), s.onreadystatechange = function() {\n    s.readyState == \"complete\" && (s.onreadystatechange = null, d());\n  });\n})(window);\nconst A = 0, H = 1, ae = 2, Be = {\n  name: \"Latte\",\n  type: \"light\",\n  palette: [\"#dd7878\", \"#ea76cb\", \"#8839ef\", \"#e64553\", \"#fe640b\", \"#df8e1d\", \"#40a02b\", \"#209fb5\", \"#1e66f5\", \"#7287fd\"],\n  cssVar: {\n    \"--gap\": \"30px\",\n    \"--main-color\": \"#444446\",\n    \"--main-bgcolor\": \"#ffffff\",\n    \"--color\": \"#777777\",\n    \"--bgcolor\": \"#f6f6f6\",\n    \"--panel-color\": \"#444446\",\n    \"--panel-bgcolor\": \"#ffffff\",\n    \"--panel-border-color\": \"#eaeaea\"\n  }\n}, Re = {\n  name: \"Dark\",\n  type: \"dark\",\n  palette: [\"#848FA0\", \"#748BE9\", \"#D2F9FE\", \"#4145A5\", \"#789AFA\", \"#706CF4\", \"#EF987F\", \"#775DD5\", \"#FCEECF\", \"#DA7FBC\"],\n  cssVar: {\n    \"--main-color\": \"#ffffff\",\n    \"--main-bgcolor\": \"#4c4f69\",\n    \"--color\": \"#cccccc\",\n    \"--bgcolor\": \"#252526\",\n    \"--panel-color\": \"#ffffff\",\n    \"--panel-bgcolor\": \"#2d3748\",\n    \"--panel-border-color\": \"#696969\"\n  }\n};\nfunction se(e) {\n  return e.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/\"/g, \"&quot;\");\n}\nconst ie = function(e, t) {\n  if (t.id === e)\n    return t;\n  if (t.children && t.children.length) {\n    for (let n = 0; n < t.children.length; n++) {\n      const o = ie(e, t.children[n]);\n      if (o)\n        return o;\n    }\n    return null;\n  } else\n    return null;\n}, P = (e, t) => {\n  if (e.parent = t, e.children)\n    for (let n = 0; n < e.children.length; n++)\n      P(e.children[n], e);\n};\nfunction fe(e) {\n  if (e.id = V(), e.children)\n    for (let t = 0; t < e.children.length; t++)\n      fe(e.children[t]);\n}\nfunction re(e, t, n, o) {\n  const s = o - t, i = e - n;\n  let r = Math.atan(Math.abs(s) / Math.abs(i)) / 3.14 * 180;\n  if (isNaN(r))\n    return;\n  i < 0 && s > 0 && (r = 180 - r), i < 0 && s < 0 && (r = 180 + r), i > 0 && s < 0 && (r = 360 - r);\n  const c = 12, a = 30, d = r + a, l = r - a;\n  return {\n    x1: n + Math.cos(Math.PI * d / 180) * c,\n    y1: o - Math.sin(Math.PI * d / 180) * c,\n    x2: n + Math.cos(Math.PI * l / 180) * c,\n    y2: o - Math.sin(Math.PI * l / 180) * c\n  };\n}\nfunction V() {\n  return ((/* @__PURE__ */ new Date()).getTime().toString(16) + Math.random().toString(16).substr(2)).substr(2, 16);\n}\nconst st = function() {\n  const e = V();\n  return {\n    topic: this.newTopicName,\n    id: e\n  };\n};\nfunction pe(e) {\n  return JSON.parse(\n    JSON.stringify(e, (n, o) => {\n      if (n !== \"parent\")\n        return o;\n    })\n  );\n}\nconst B = (e, t) => {\n  let n = 0, o = 0;\n  for (; t && t !== e; )\n    n += t.offsetLeft, o += t.offsetTop, t = t.offsetParent;\n  return { offsetLeft: n, offsetTop: o };\n}, N = (e, t) => {\n  for (const n in t)\n    e.setAttribute(n, t[n]);\n}, ze = (e) => e ? e.tagName === \"ME-TPC\" : !1, de = (e) => e.filter((t) => t.nodeObj.parent).filter((t, n, o) => {\n  for (let s = 0; s < o.length; s++) {\n    if (t === o[s])\n      continue;\n    const { parent: i } = t.nodeObj;\n    if (i === o[s].nodeObj)\n      return !1;\n  }\n  return !0;\n}), Fe = (e) => {\n  const t = /translate\\(([^,]+),\\s*([^)]+)\\)/, n = e.match(t);\n  return n ? { x: parseFloat(n[1]), y: parseFloat(n[2]) } : { x: 0, y: 0 };\n}, me = function(e) {\n  for (let t = 0; t < e.length; t++) {\n    const { dom: n, evt: o, func: s } = e[t];\n    n.addEventListener(o, s);\n  }\n  return function() {\n    for (let n = 0; n < e.length; n++) {\n      const { dom: o, evt: s, func: i } = e[n];\n      o.removeEventListener(s, i);\n    }\n  };\n};\nfunction it(e) {\n  const { dragMoveHelper: t } = e;\n  let n = 0, o = 0;\n  const s = (f) => {\n    var g, y, x;\n    if (f instanceof TouchEvent || f.button !== 0)\n      return;\n    if ((g = e.helper1) != null && g.moved) {\n      e.helper1.clear();\n      return;\n    }\n    if ((y = e.helper2) != null && y.moved) {\n      e.helper2.clear();\n      return;\n    }\n    if (t.moved) {\n      t.clear();\n      return;\n    }\n    const p = f.target;\n    if (p.tagName === \"ME-EPD\")\n      f.ctrlKey || f.metaKey ? e.expandNodeAll(p.previousSibling) : e.expandNode(p.previousSibling);\n    else if (p.tagName === \"ME-TPC\" && e.currentNodes.length > 1)\n      e.selectNode(p);\n    else if (!e.editable)\n      return;\n    const m = (x = p.parentElement) == null ? void 0 : x.parentElement;\n    m.getAttribute(\"class\") === \"topiclinks\" ? e.selectArrow(p.parentElement) : m.getAttribute(\"class\") === \"summary\" && e.selectSummary(p.parentElement);\n  }, i = (f) => {\n    var g;\n    if (!e.editable)\n      return;\n    const p = f.target;\n    ze(p) && e.beginEdit(p);\n    const m = (g = p.parentElement) == null ? void 0 : g.parentElement;\n    m.getAttribute(\"class\") === \"topiclinks\" ? e.editArrowLabel(p.parentElement) : m.getAttribute(\"class\") === \"summary\" && e.editSummary(p.parentElement);\n  };\n  let r = 0;\n  const c = (f) => {\n    const p = (/* @__PURE__ */ new Date()).getTime(), m = p - r;\n    m < 300 && m > 0 && i(f), r = p;\n  }, a = (f) => {\n    var p, m;\n    if (f instanceof MouseEvent) {\n      const g = e.mouseSelectionButton === 0 ? 2 : 0;\n      if (f.button !== g)\n        return;\n    } else\n      n = ((p = f.touches[0]) == null ? void 0 : p.clientX) || 0, o = ((m = f.touches[0]) == null ? void 0 : m.clientY) || 0;\n    f.target.contentEditable === \"inherit\" && (t.moved = !1, t.mousedown = !0, e.map.style.transition = \"none\");\n  }, d = (f) => {\n    var p, m, g, y;\n    if (f.target.contentEditable === \"inherit\")\n      if (f instanceof MouseEvent)\n        t.onMove(f), t.x = f.clientX, t.y = f.clientY;\n      else {\n        const x = ((p = f.touches[0]) == null ? void 0 : p.clientX) || 0, E = ((m = f.touches[0]) == null ? void 0 : m.clientY) || 0, b = x - n, w = E - o, _ = {\n          clientX: x,\n          clientY: E,\n          movementX: b,\n          movementY: w,\n          target: f.target\n        };\n        t.onMove(_), t.x = x, t.y = E, n = x, o = E;\n      }\n    else if (f instanceof MouseEvent)\n      t.x = f.clientX, t.y = f.clientY;\n    else {\n      const x = ((g = f.touches[0]) == null ? void 0 : g.clientX) || 0, E = ((y = f.touches[0]) == null ? void 0 : y.clientY) || 0;\n      t.x = x, t.y = E, n = x, o = E;\n    }\n  }, l = (f) => {\n    if (f instanceof MouseEvent) {\n      const p = e.mouseSelectionButton === 0 ? 2 : 0;\n      if (f.button !== p)\n        return;\n    }\n    t.clear();\n  }, u = (f) => {\n    f instanceof TouchEvent || t.moved && f.preventDefault();\n  }, h = e.map;\n  return me([\n    { dom: h, evt: \"click\", func: s },\n    { dom: h, evt: \"dblclick\", func: i },\n    { dom: h, evt: \"mousedown\", func: a },\n    // to handle mouse move outside of map, add event listener to document\n    { dom: document, evt: \"mousemove\", func: d },\n    { dom: document, evt: \"mouseup\", func: l },\n    { dom: document, evt: \"contextmenu\", func: u },\n    // support touch events\n    { dom: h, evt: \"touchstart\", func: a },\n    { dom: document, evt: \"touchmove\", func: d },\n    { dom: document, evt: \"touchend\", func: l },\n    { dom: h, evt: \"touchend\", func: c }\n  ]);\n}\nfunction rt() {\n  return {\n    handlers: {},\n    addListener: function(e, t) {\n      this.handlers[e] === void 0 && (this.handlers[e] = []), this.handlers[e].push(t);\n    },\n    fire: function(e, ...t) {\n      if (this.handlers[e] instanceof Array) {\n        const n = this.handlers[e];\n        for (let o = 0; o < n.length; o++)\n          n[o](...t);\n      }\n    },\n    removeListener: function(e, t) {\n      if (!this.handlers[e])\n        return;\n      const n = this.handlers[e];\n      if (!t)\n        n.length = 0;\n      else if (n.length)\n        for (let o = 0; o < n.length; o++)\n          n[o] === t && this.handlers[e].splice(o, 1);\n    }\n  };\n}\nvar M = /* @__PURE__ */ ((e) => (e.LHS = \"lhs\", e.RHS = \"rhs\", e))(M || {});\nconst ce = document, ct = function() {\n  this.nodes.innerHTML = \"\";\n  const e = this.createTopic(this.nodeData);\n  ge(e, this.nodeData), e.draggable = !1;\n  const t = ce.createElement(\"me-root\");\n  t.appendChild(e);\n  const n = this.nodeData.children || [];\n  if (this.direction === ae) {\n    let o = 0, s = 0;\n    n.map((i) => {\n      i.direction === A ? o += 1 : i.direction === H ? s += 1 : o <= s ? (i.direction = A, o += 1) : (i.direction = H, s += 1);\n    });\n  }\n  lt(this, n, t);\n}, lt = function(e, t, n) {\n  const o = ce.createElement(\"me-main\");\n  o.className = M.LHS;\n  const s = ce.createElement(\"me-main\");\n  s.className = M.RHS;\n  for (let i = 0; i < t.length; i++) {\n    const r = t[i], { grp: c } = e.createWrapper(r);\n    e.direction === ae ? r.direction === A ? o.appendChild(c) : s.appendChild(c) : e.direction === A ? o.appendChild(c) : s.appendChild(c);\n  }\n  e.nodes.appendChild(o), e.nodes.appendChild(n), e.nodes.appendChild(s), e.nodes.appendChild(e.lines);\n}, at = function(e, t) {\n  const n = ce.createElement(\"me-children\");\n  for (let o = 0; o < t.length; o++) {\n    const s = t[o], { grp: i } = e.createWrapper(s);\n    n.appendChild(i);\n  }\n  return n;\n}, k = document, S = (e, t) => {\n  const o = (t ? t.el : k).querySelector(`[data-nodeid=me${e}]`);\n  if (!o)\n    throw new Error(`FindEle: Node ${e} not found, maybe it's collapsed.`);\n  return o;\n}, ge = function(e, t) {\n  if (e.innerHTML = \"\", t.style && (e.style.color = t.style.color || \"\", e.style.background = t.style.background || \"\", e.style.fontSize = t.style.fontSize + \"px\", e.style.fontWeight = t.style.fontWeight || \"normal\"), t.dangerouslySetInnerHTML) {\n    e.innerHTML = t.dangerouslySetInnerHTML;\n    return;\n  }\n  if (t.image) {\n    const n = t.image;\n    if (n.url && n.width && n.height) {\n      const o = k.createElement(\"img\");\n      o.src = n.url, o.style.width = n.width + \"px\", o.style.height = n.height + \"px\", n.fit && (o.style.objectFit = n.fit), e.appendChild(o), e.image = o;\n    }\n  } else\n    e.image && (e.image = void 0);\n  {\n    const n = k.createElement(\"span\");\n    n.className = \"text\", n.textContent = t.topic, e.appendChild(n), e.text = n;\n  }\n  if (t.hyperLink) {\n    const n = k.createElement(\"a\");\n    n.className = \"hyper-link\", n.target = \"_blank\", n.innerText = \"🔗\", n.href = t.hyperLink, e.appendChild(n), e.link = n;\n  } else\n    e.link && (e.link = void 0);\n  if (t.icons && t.icons.length) {\n    const n = k.createElement(\"span\");\n    n.className = \"icons\", n.innerHTML = t.icons.map((o) => `<span>${se(o)}</span>`).join(\"\"), e.appendChild(n), e.icons = n;\n  } else\n    e.icons && (e.icons = void 0);\n  if (t.tags && t.tags.length) {\n    const n = k.createElement(\"div\");\n    n.className = \"tags\", n.innerHTML = t.tags.map((o) => `<span>${se(o)}</span>`).join(\"\"), e.appendChild(n), e.tags = n;\n  } else\n    e.tags && (e.tags = void 0);\n}, dt = function(e, t) {\n  const n = k.createElement(\"me-wrapper\"), { p: o, tpc: s } = this.createParent(e);\n  if (n.appendChild(o), !t && e.children && e.children.length > 0) {\n    const i = ve(e.expanded);\n    if (o.appendChild(i), e.expanded !== !1) {\n      const r = at(this, e.children);\n      n.appendChild(r);\n    }\n  }\n  return { grp: n, top: o, tpc: s };\n}, ht = function(e) {\n  const t = k.createElement(\"me-parent\"), n = this.createTopic(e);\n  return ge(n, e), t.appendChild(n), { p: t, tpc: n };\n}, ut = function(e) {\n  const t = k.createElement(\"me-children\");\n  return t.append(...e), t;\n}, ft = function(e) {\n  const t = k.createElement(\"me-tpc\");\n  return t.nodeObj = e, t.dataset.nodeid = \"me\" + e.id, t.draggable = this.draggable, t;\n};\nfunction qe(e) {\n  const t = k.createRange();\n  t.selectNodeContents(e);\n  const n = window.getSelection();\n  n && (n.removeAllRanges(), n.addRange(t));\n}\nconst pt = function(e) {\n  if (!e)\n    return;\n  const t = k.createElement(\"div\"), n = e.text.textContent;\n  e.appendChild(t), t.id = \"input-box\", t.textContent = n, t.contentEditable = \"plaintext-only\", t.spellcheck = !1;\n  const o = getComputedStyle(e);\n  t.style.cssText = `min-width:${e.offsetWidth - 8}px;\n  color:${o.color};\n  padding:${o.padding};\n  margin:${o.margin};\n  font:${o.font};\n  background-color:${o.backgroundColor !== \"rgba(0, 0, 0, 0)\" && o.backgroundColor};\n  border-radius:${o.borderRadius};`, this.direction === A && (t.style.right = \"0\"), qe(t), this.bus.fire(\"operation\", {\n    name: \"beginEdit\",\n    obj: e.nodeObj\n  }), t.addEventListener(\"keydown\", (s) => {\n    s.stopPropagation();\n    const i = s.key;\n    if (i === \"Enter\" || i === \"Tab\") {\n      if (s.shiftKey)\n        return;\n      s.preventDefault(), t.blur(), this.map.focus();\n    }\n  }), t.addEventListener(\"blur\", () => {\n    var r;\n    if (!t)\n      return;\n    const s = e.nodeObj, i = ((r = t.textContent) == null ? void 0 : r.trim()) || \"\";\n    i === \"\" ? s.topic = n : s.topic = i, t.remove(), i !== n && (e.text.textContent = s.topic, this.linkDiv(), this.bus.fire(\"operation\", {\n      name: \"finishEdit\",\n      obj: s,\n      origin: n\n    }));\n  });\n}, ve = function(e) {\n  const t = k.createElement(\"me-epd\");\n  return t.expanded = e !== !1, t.className = e !== !1 ? \"minus\" : \"\", t;\n}, q = document, W = \"http://www.w3.org/2000/svg\", Ve = function(e, t, n) {\n  const o = q.createElementNS(W, \"path\");\n  return N(o, {\n    d: e,\n    stroke: t || \"#666\",\n    fill: \"none\",\n    \"stroke-width\": n\n  }), o;\n}, Z = function(e) {\n  const t = q.createElementNS(W, \"svg\");\n  return t.setAttribute(\"class\", e), t.setAttribute(\"overflow\", \"visible\"), t;\n}, Ce = function() {\n  const e = q.createElementNS(W, \"line\");\n  return e.setAttribute(\"stroke\", \"#bbb\"), e.setAttribute(\"fill\", \"none\"), e.setAttribute(\"stroke-width\", \"2\"), e;\n}, mt = function(e, t, n) {\n  const o = q.createElementNS(W, \"g\");\n  return [\n    {\n      name: \"line\",\n      d: e\n    },\n    {\n      name: \"arrow1\",\n      d: t\n    },\n    {\n      name: \"arrow2\",\n      d: n\n    }\n  ].forEach((i, r) => {\n    const c = i.d, a = q.createElementNS(W, \"path\");\n    N(a, {\n      d: c,\n      stroke: \"rgb(235, 95, 82)\",\n      fill: \"none\",\n      \"stroke-linecap\": \"cap\",\n      \"stroke-width\": \"2\"\n    }), r === 0 && a.setAttribute(\"stroke-dasharray\", \"8,2\");\n    const l = q.createElementNS(W, \"path\");\n    N(l, {\n      d: c,\n      stroke: \"transparent\",\n      fill: \"none\",\n      \"stroke-width\": \"15\"\n    }), o.appendChild(l), o.appendChild(a), o[i.name] = a;\n  }), o;\n}, Ie = function(e, t, n) {\n  if (!t)\n    return;\n  const o = q.createElement(\"div\");\n  e.nodes.appendChild(o);\n  const s = t.innerHTML;\n  o.id = \"input-box\", o.textContent = s, o.contentEditable = \"plaintext-only\", o.spellcheck = !1;\n  const i = t.getBBox();\n  o.style.cssText = `\n    min-width:${Math.max(88, i.width)}px;\n    position:absolute;\n    left:${i.x}px;\n    top:${i.y}px;\n    padding: 2px 4px;\n    margin: -2px -4px; \n  `, qe(o), e.scrollIntoView(o), o.addEventListener(\"keydown\", (r) => {\n    r.stopPropagation();\n    const c = r.key;\n    if (c === \"Enter\" || c === \"Tab\") {\n      if (r.shiftKey)\n        return;\n      r.preventDefault(), o.blur(), e.map.focus();\n    }\n  }), o.addEventListener(\"blur\", () => {\n    var c;\n    if (!o)\n      return;\n    const r = ((c = o.textContent) == null ? void 0 : c.trim()) || \"\";\n    r === \"\" ? n.label = s : n.label = r, o.remove(), r !== s && (t.innerHTML = n.label, e.linkDiv(), \"parent\" in n ? e.bus.fire(\"operation\", {\n      name: \"finishEditSummary\",\n      obj: n\n    }) : e.bus.fire(\"operation\", {\n      name: \"finishEditArrowLabel\",\n      obj: n\n    }));\n  });\n}, gt = function(e) {\n  const t = this.map.querySelector(\"me-root\"), n = t.offsetTop, o = t.offsetLeft, s = t.offsetWidth, i = t.offsetHeight, c = this.nodes.offsetWidth;\n  this.nodes.style.top = `${1e4 - this.nodes.offsetHeight / 2}px`, this.alignment === \"root\" ? this.nodes.style.left = `${1e4 - o - s / 2}px` : this.nodes.style.left = `${1e4 - c / 2}px`;\n  const a = this.map.querySelectorAll(\"me-main > me-wrapper\");\n  this.lines.innerHTML = \"\";\n  for (let d = 0; d < a.length; d++) {\n    const l = a[d], u = l.querySelector(\"me-tpc\"), { offsetLeft: h, offsetTop: v } = B(this.nodes, u), f = u.offsetWidth, p = u.offsetHeight, m = l.parentNode.className, g = this.generateMainBranch({ pT: n, pL: o, pW: s, pH: i, cT: v, cL: h, cW: f, cH: p, direction: m, containerHeight: this.nodes.offsetHeight }), y = this.theme.palette, x = u.nodeObj.branchColor || y[d % y.length];\n    u.style.borderColor = x, this.lines.appendChild(Ve(g, x, \"3\"));\n    const E = l.children[0].children[1];\n    if (E && (E.style.top = (E.parentNode.offsetHeight - E.offsetHeight) / 2 + \"px\", m === M.LHS ? E.style.left = \"-10px\" : E.style.right = \"-10px\"), e && e !== l)\n      continue;\n    const b = Z(\"subLines\"), w = l.lastChild;\n    w.tagName === \"svg\" && w.remove(), l.appendChild(b), Ke(this, b, x, l, m, !0);\n  }\n  this.renderArrow(), this.renderSummary(), this.bus.fire(\"linkDiv\");\n}, Ke = function(e, t, n, o, s, i) {\n  const r = o.firstChild, c = o.children[1].children;\n  if (c.length === 0)\n    return;\n  const a = r.offsetTop, d = r.offsetLeft, l = r.offsetWidth, u = r.offsetHeight;\n  for (let h = 0; h < c.length; h++) {\n    const v = c[h], f = v.firstChild, p = f.offsetTop, m = f.offsetLeft, g = f.offsetWidth, y = f.offsetHeight, x = f.firstChild.nodeObj.branchColor || n, E = e.generateSubBranch({ pT: a, pL: d, pW: l, pH: u, cT: p, cL: m, cW: g, cH: y, direction: s, isFirst: i });\n    t.appendChild(Ve(E, x, \"2\"));\n    const b = f.children[1];\n    if (b) {\n      if (b.style.bottom = -(b.offsetHeight / 2) + \"px\", s === M.LHS ? b.style.left = \"10px\" : s === M.RHS && (b.style.right = \"10px\"), !b.expanded)\n        continue;\n    } else\n      continue;\n    Ke(e, t, x, v, s);\n  }\n}, Ne = {\n  addChild: \"插入子节点\",\n  addParent: \"插入父节点\",\n  addSibling: \"插入同级节点\",\n  removeNode: \"删除节点\",\n  focus: \"专注\",\n  cancelFocus: \"取消专注\",\n  moveUp: \"上移\",\n  moveDown: \"下移\",\n  link: \"连接\",\n  clickTips: \"请点击目标节点\",\n  summary: \"摘要\"\n}, Se = {\n  cn: Ne,\n  zh_CN: Ne,\n  zh_TW: {\n    addChild: \"插入子節點\",\n    addParent: \"插入父節點\",\n    addSibling: \"插入同級節點\",\n    removeNode: \"刪除節點\",\n    focus: \"專注\",\n    cancelFocus: \"取消專注\",\n    moveUp: \"上移\",\n    moveDown: \"下移\",\n    link: \"連接\",\n    clickTips: \"請點擊目標節點\",\n    summary: \"摘要\"\n  },\n  en: {\n    addChild: \"Add child\",\n    addParent: \"Add parent\",\n    addSibling: \"Add sibling\",\n    removeNode: \"Remove node\",\n    focus: \"Focus Mode\",\n    cancelFocus: \"Cancel Focus Mode\",\n    moveUp: \"Move up\",\n    moveDown: \"Move down\",\n    link: \"Link\",\n    clickTips: \"Please click the target node\",\n    summary: \"Summary\"\n  },\n  ru: {\n    addChild: \"Добавить дочерний элемент\",\n    addParent: \"Добавить родительский элемент\",\n    addSibling: \"Добавить на этом уровне\",\n    removeNode: \"Удалить узел\",\n    focus: \"Режим фокусировки\",\n    cancelFocus: \"Отменить режим фокусировки\",\n    moveUp: \"Поднять выше\",\n    moveDown: \"Опустить ниже\",\n    link: \"Ссылка\",\n    clickTips: \"Пожалуйста, нажмите на целевой узел\",\n    summary: \"Описание\"\n  },\n  ja: {\n    addChild: \"子ノードを追加する\",\n    addParent: \"親ノードを追加します\",\n    addSibling: \"兄弟ノードを追加する\",\n    removeNode: \"ノードを削除\",\n    focus: \"集中\",\n    cancelFocus: \"集中解除\",\n    moveUp: \"上へ移動\",\n    moveDown: \"下へ移動\",\n    link: \"コネクト\",\n    clickTips: \"ターゲットノードをクリックしてください\",\n    summary: \"概要\"\n  },\n  pt: {\n    addChild: \"Adicionar item filho\",\n    addParent: \"Adicionar item pai\",\n    addSibling: \"Adicionar item irmao\",\n    removeNode: \"Remover item\",\n    focus: \"Modo Foco\",\n    cancelFocus: \"Cancelar Modo Foco\",\n    moveUp: \"Mover para cima\",\n    moveDown: \"Mover para baixo\",\n    link: \"Link\",\n    clickTips: \"Favor clicar no item alvo\",\n    summary: \"Resumo\"\n  },\n  it: {\n    addChild: \"Aggiungi figlio\",\n    addParent: \"Aggiungi genitore\",\n    addSibling: \"Aggiungi fratello\",\n    removeNode: \"Rimuovi nodo\",\n    focus: \"Modalità Focus\",\n    cancelFocus: \"Annulla Modalità Focus\",\n    moveUp: \"Sposta su\",\n    moveDown: \"Sposta giù\",\n    link: \"Collega\",\n    clickTips: \"Si prega di fare clic sul nodo di destinazione\",\n    summary: \"Unisci nodi\"\n  },\n  es: {\n    addChild: \"Agregar hijo\",\n    addParent: \"Agregar padre\",\n    addSibling: \"Agregar hermano\",\n    removeNode: \"Eliminar nodo\",\n    focus: \"Modo Enfoque\",\n    cancelFocus: \"Cancelar Modo Enfoque\",\n    moveUp: \"Mover hacia arriba\",\n    moveDown: \"Mover hacia abajo\",\n    link: \"Enlace\",\n    clickTips: \"Por favor haga clic en el nodo de destino\",\n    summary: \"Resumen\"\n  },\n  fr: {\n    addChild: \"Ajout enfant\",\n    addParent: \"Ajout parent\",\n    addSibling: \"Ajout voisin\",\n    removeNode: \"Supprimer\",\n    focus: \"Cibler\",\n    cancelFocus: \"Retour\",\n    moveUp: \"Monter\",\n    moveDown: \"Descendre\",\n    link: \"Lier\",\n    clickTips: \"Cliquer sur le noeud cible\",\n    summary: \"Annoter\"\n  },\n  ko: {\n    addChild: \"자식 추가\",\n    addParent: \"부모 추가\",\n    addSibling: \"형제 추가\",\n    removeNode: \"노드 삭제\",\n    focus: \"포커스 모드\",\n    cancelFocus: \"포커스 모드 취소\",\n    moveUp: \"위로 이동\",\n    moveDown: \"아래로 이동\",\n    link: \"연결\",\n    clickTips: \"대상 노드를 클릭하십시오\",\n    summary: \"요약\"\n  }\n};\nfunction vt(e, t) {\n  t = t === !0 ? {\n    focus: !0,\n    link: !0\n  } : t;\n  const n = (b) => {\n    const w = document.createElement(\"div\");\n    return w.innerText = b, w.className = \"tips\", w;\n  }, o = (b, w, _) => {\n    const T = document.createElement(\"li\");\n    return T.id = b, T.innerHTML = `<span>${se(w)}</span><span ${_ ? 'class=\"key\"' : \"\"}>${se(_)}</span>`, T;\n  }, s = Se[e.locale] ? e.locale : \"en\", i = Se[s], r = o(\"cm-add_child\", i.addChild, \"Tab\"), c = o(\"cm-add_parent\", i.addParent, \"Ctrl + Enter\"), a = o(\"cm-add_sibling\", i.addSibling, \"Enter\"), d = o(\"cm-remove_child\", i.removeNode, \"Delete\"), l = o(\"cm-fucus\", i.focus, \"\"), u = o(\"cm-unfucus\", i.cancelFocus, \"\"), h = o(\"cm-up\", i.moveUp, \"PgUp\"), v = o(\"cm-down\", i.moveDown, \"Pgdn\"), f = o(\"cm-link\", i.link, \"\"), p = o(\"cm-link-bidirectional\", \"Bidirectional Link\", \"\"), m = o(\"cm-summary\", i.summary, \"\"), g = document.createElement(\"ul\");\n  if (g.className = \"menu-list\", g.appendChild(r), g.appendChild(c), g.appendChild(a), g.appendChild(d), t.focus && (g.appendChild(l), g.appendChild(u)), g.appendChild(h), g.appendChild(v), g.appendChild(m), t.link && (g.appendChild(f), g.appendChild(p)), t && t.extend)\n    for (let b = 0; b < t.extend.length; b++) {\n      const w = t.extend[b], _ = o(w.name, w.name, w.key || \"\");\n      g.appendChild(_), _.onclick = (T) => {\n        w.onclick(T);\n      };\n    }\n  const y = document.createElement(\"div\");\n  y.className = \"context-menu\", y.appendChild(g), y.hidden = !0, e.container.append(y);\n  let x = !0;\n  e.container.oncontextmenu = function(b) {\n    if (b.preventDefault(), !e.editable || e.dragMoveHelper.moved)\n      return;\n    const w = b.target;\n    if (ze(w)) {\n      w.parentElement.tagName === \"ME-ROOT\" ? x = !0 : x = !1, x ? (l.className = \"disabled\", h.className = \"disabled\", v.className = \"disabled\", c.className = \"disabled\", a.className = \"disabled\", d.className = \"disabled\") : (l.className = \"\", h.className = \"\", v.className = \"\", c.className = \"\", a.className = \"\", d.className = \"\"), w.classList.contains(\"selected\") || e.selectNode(w), y.hidden = !1, g.style.top = \"\", g.style.bottom = \"\", g.style.left = \"\", g.style.right = \"\";\n      const _ = g.getBoundingClientRect(), T = g.offsetHeight, $ = g.offsetWidth, R = b.clientY - _.top, U = b.clientX - _.left;\n      T + R > window.innerHeight ? (g.style.top = \"\", g.style.bottom = \"0px\") : (g.style.bottom = \"\", g.style.top = R + 15 + \"px\"), $ + U > window.innerWidth ? (g.style.left = \"\", g.style.right = \"0px\") : (g.style.right = \"\", g.style.left = U + 10 + \"px\");\n    }\n  }, y.onclick = (b) => {\n    b.target === y && (y.hidden = !0);\n  }, r.onclick = () => {\n    e.addChild(), y.hidden = !0;\n  }, c.onclick = () => {\n    e.insertParent(), y.hidden = !0;\n  }, a.onclick = () => {\n    x || (e.insertSibling(\"after\"), y.hidden = !0);\n  }, d.onclick = () => {\n    x || (e.removeNodes(e.currentNodes || []), y.hidden = !0);\n  }, l.onclick = () => {\n    x || (e.focusNode(e.currentNode), y.hidden = !0);\n  }, u.onclick = () => {\n    e.cancelFocus(), y.hidden = !0;\n  }, h.onclick = () => {\n    x || (e.moveUpNode(), y.hidden = !0);\n  }, v.onclick = () => {\n    x || (e.moveDownNode(), y.hidden = !0);\n  };\n  const E = (b) => {\n    y.hidden = !0;\n    const w = e.currentNode, _ = n(i.clickTips);\n    e.container.appendChild(_), e.map.addEventListener(\n      \"click\",\n      (T) => {\n        T.preventDefault(), _.remove();\n        const $ = T.target;\n        ($.parentElement.tagName === \"ME-PARENT\" || $.parentElement.tagName === \"ME-ROOT\") && e.createArrow(w, $, b);\n      },\n      {\n        once: !0\n      }\n    );\n  };\n  return f.onclick = () => E(), p.onclick = () => E({ bidirectional: !0 }), m.onclick = () => {\n    y.hidden = !0, e.createSummary(), e.unselectNodes(e.currentNodes);\n  }, () => {\n    r.onclick = null, c.onclick = null, a.onclick = null, d.onclick = null, l.onclick = null, u.onclick = null, h.onclick = null, v.onclick = null, f.onclick = null, m.onclick = null, y.onclick = null, e.container.oncontextmenu = null;\n  };\n}\nconst yt = (e) => {\n  const t = e.map.querySelectorAll(\".lhs>me-wrapper>me-parent>me-tpc\");\n  e.selectNode(t[Math.ceil(t.length / 2) - 1]);\n}, bt = (e) => {\n  const t = e.map.querySelectorAll(\".rhs>me-wrapper>me-parent>me-tpc\");\n  e.selectNode(t[Math.ceil(t.length / 2) - 1]);\n}, xt = (e) => {\n  e.selectNode(e.map.querySelector(\"me-root>me-tpc\"));\n}, wt = function(e, t) {\n  const n = t.parentElement.parentElement.parentElement.previousSibling;\n  if (n) {\n    const o = n.firstChild;\n    e.selectNode(o);\n  }\n}, Et = function(e, t) {\n  const n = t.parentElement.nextSibling;\n  if (n && n.firstChild) {\n    const o = n.firstChild.firstChild.firstChild;\n    e.selectNode(o);\n  }\n}, _e = function(e, t) {\n  var i, r;\n  const n = e.currentNode || ((i = e.currentNodes) == null ? void 0 : i[0]);\n  if (!n)\n    return;\n  const o = n.nodeObj, s = n.offsetParent.offsetParent.parentElement;\n  o.parent ? s.className === t ? Et(e, n) : (r = o.parent) != null && r.parent ? wt(e, n) : xt(e) : t === M.LHS ? yt(e) : bt(e);\n}, Me = function(e, t) {\n  const n = e.currentNode;\n  if (!n || !n.nodeObj.parent)\n    return;\n  const s = t + \"Sibling\", i = n.parentElement.parentElement[s];\n  i ? e.selectNode(i.firstChild.firstChild) : e.selectNode(n);\n}, ee = function(e, t, n) {\n  switch (t) {\n    case \"in\":\n      if (e.scaleVal > 1.6)\n        return;\n      e.scale(e.scaleVal + 0.2, n);\n      break;\n    case \"out\":\n      if (e.scaleVal < 0.6)\n        return;\n      e.scale(e.scaleVal - 0.2, n);\n  }\n};\nfunction Ct(e, t) {\n  t = t === !0 ? {} : t;\n  const n = () => {\n    e.currentArrow ? e.removeArrow() : e.currentSummary ? e.removeSummary(e.currentSummary.summaryObj.id) : e.currentNodes && e.removeNodes(e.currentNodes);\n  }, o = {\n    Enter: (s) => {\n      s.shiftKey ? e.insertSibling(\"before\") : s.ctrlKey ? e.insertParent() : e.insertSibling(\"after\");\n    },\n    Tab: () => {\n      e.addChild();\n    },\n    F1: () => {\n      e.toCenter();\n    },\n    F2: () => {\n      e.beginEdit();\n    },\n    ArrowUp: (s) => {\n      if (s.altKey)\n        e.moveUpNode();\n      else {\n        if (s.metaKey || s.ctrlKey)\n          return e.initSide();\n        Me(e, \"previous\");\n      }\n    },\n    ArrowDown: (s) => {\n      s.altKey ? e.moveDownNode() : Me(e, \"next\");\n    },\n    ArrowLeft: (s) => {\n      if (s.metaKey || s.ctrlKey)\n        return e.initLeft();\n      _e(e, M.LHS);\n    },\n    ArrowRight: (s) => {\n      if (s.metaKey || s.ctrlKey)\n        return e.initRight();\n      _e(e, M.RHS);\n    },\n    PageUp: () => e.moveUpNode(),\n    PageDown: () => {\n      e.moveDownNode();\n    },\n    c: (s) => {\n      (s.metaKey || s.ctrlKey) && (e.waitCopy = e.currentNodes);\n    },\n    x: (s) => {\n      (s.metaKey || s.ctrlKey) && (e.waitCopy = e.currentNodes, n());\n    },\n    v: (s) => {\n      !e.waitCopy || !e.currentNode || (s.metaKey || s.ctrlKey) && (e.waitCopy.length === 1 ? e.copyNode(e.waitCopy[0], e.currentNode) : e.copyNodes(e.waitCopy, e.currentNode));\n    },\n    \"=\": (s) => {\n      (s.metaKey || s.ctrlKey) && ee(e, \"in\");\n    },\n    \"-\": (s) => {\n      (s.metaKey || s.ctrlKey) && ee(e, \"out\");\n    },\n    0: (s) => {\n      (s.metaKey || s.ctrlKey) && e.scale(1);\n    },\n    Delete: n,\n    Backspace: n,\n    ...t\n  };\n  e.map.onkeydown = (s) => {\n    if (s.preventDefault(), !e.editable || s.target !== s.currentTarget)\n      return;\n    const i = o[s.key];\n    i && i(s);\n  }, e.map.onwheel = (s) => {\n    s.stopPropagation(), s.preventDefault(), s.ctrlKey || s.metaKey ? s.deltaY < 0 ? ee(e, \"in\", e.dragMoveHelper) : e.scaleVal - 0.2 > 0 && ee(e, \"out\", e.dragMoveHelper) : s.shiftKey ? e.move(-s.deltaY, 0) : e.move(0, -s.deltaY);\n  };\n}\nconst he = document, Nt = function(e, t) {\n  if (!t)\n    return ue(e), e;\n  let n = e.querySelector(\".insert-preview\");\n  const o = `insert-preview ${t} show`;\n  return n || (n = he.createElement(\"div\"), e.appendChild(n)), n.className = o, e;\n}, ue = function(e) {\n  if (!e)\n    return;\n  const t = e.querySelectorAll(\".insert-preview\");\n  for (const n of t || [])\n    n.remove();\n}, Te = function(e, t) {\n  for (const n of t) {\n    const o = n.parentElement.parentElement.contains(e);\n    if (!(e && e.tagName === \"ME-TPC\" && e !== n && !o && e.nodeObj.parent))\n      return !1;\n  }\n  return !0;\n}, St = function(e) {\n  const t = document.createElement(\"div\");\n  return t.className = \"mind-elixir-ghost\", e.map.appendChild(t), t;\n};\nclass _t {\n  constructor(t) {\n    G(this, \"mind\");\n    G(this, \"isMoving\", !1);\n    G(this, \"interval\", null);\n    G(this, \"speed\", 20);\n    this.mind = t;\n  }\n  move(t, n) {\n    this.isMoving || (this.isMoving = !0, this.interval = setInterval(() => {\n      this.mind.move(t * this.speed * this.mind.scaleVal, n * this.speed * this.mind.scaleVal);\n    }, 100));\n  }\n  stop() {\n    this.isMoving = !1, clearInterval(this.interval);\n  }\n}\nfunction Mt(e) {\n  let t = null, n = null;\n  const o = St(e), s = new _t(e), i = (d) => {\n    e.selection.cancel();\n    const l = d.target;\n    if ((l == null ? void 0 : l.tagName) !== \"ME-TPC\") {\n      d.preventDefault();\n      return;\n    }\n    let u = e.currentNodes;\n    u != null && u.includes(l) || (e.selectNode(l), u = e.currentNodes), e.dragged = u, u.length > 1 ? o.innerHTML = u.length + \"\" : o.innerHTML = l.innerHTML;\n    for (const h of u)\n      h.parentElement.parentElement.style.opacity = \"0.5\";\n    d.dataTransfer.setDragImage(o, 0, 0), d.dataTransfer.dropEffect = \"move\", e.dragMoveHelper.clear();\n  }, r = (d) => {\n    const { dragged: l } = e;\n    if (!l)\n      return;\n    s.stop();\n    for (const h of l)\n      h.parentElement.parentElement.style.opacity = \"1\";\n    const u = d.target;\n    u.style.opacity = \"\", n && (ue(n), t === \"before\" ? e.moveNodeBefore(l, n) : t === \"after\" ? e.moveNodeAfter(l, n) : t === \"in\" && e.moveNodeIn(l, n), e.dragged = null);\n  }, c = (d) => {\n    d.preventDefault();\n    const l = 12 * e.scaleVal, { dragged: u } = e;\n    if (!u)\n      return;\n    const h = e.container.getBoundingClientRect();\n    d.clientX < h.x + 50 ? s.move(1, 0) : d.clientX > h.x + h.width - 50 ? s.move(-1, 0) : d.clientY < h.y + 50 ? s.move(0, 1) : d.clientY > h.y + h.height - 50 ? s.move(0, -1) : s.stop(), ue(n);\n    const v = he.elementFromPoint(d.clientX, d.clientY - l);\n    if (Te(v, u)) {\n      n = v;\n      const f = v.getBoundingClientRect(), p = f.y;\n      d.clientY > p + f.height ? t = \"after\" : t = \"in\";\n    } else {\n      const f = he.elementFromPoint(d.clientX, d.clientY + l), p = f.getBoundingClientRect();\n      if (Te(f, u)) {\n        n = f;\n        const m = p.y;\n        d.clientY < m ? t = \"before\" : t = \"in\";\n      } else\n        t = n = null;\n    }\n    n && Nt(n, t);\n  };\n  return me([\n    { dom: e.map, evt: \"dragstart\", func: i },\n    { dom: e.map, evt: \"dragend\", func: r },\n    { dom: e.map, evt: \"dragover\", func: c }\n  ]);\n}\nconst Tt = function(e) {\n  return [\"createSummary\", \"removeSummary\", \"finishEditSummary\"].includes(e.name) ? {\n    type: \"summary\",\n    value: e.obj.id\n  } : [\"createArrow\", \"removeArrow\", \"finishEditArrowLabel\"].includes(e.name) ? {\n    type: \"arrow\",\n    value: e.obj.id\n  } : [\"removeNodes\", \"copyNodes\", \"moveNodeBefore\", \"moveNodeAfter\", \"moveNodeIn\"].includes(e.name) ? {\n    type: \"nodes\",\n    value: e.objs.map((t) => t.id)\n  } : {\n    type: \"node\",\n    value: e.obj.id\n  };\n};\nfunction kt(e) {\n  let t = [], n = -1, o = e.getData();\n  e.undo = function() {\n    if (n > -1) {\n      const r = t[n];\n      o = r.prev, e.refresh(r.prev);\n      try {\n        r.currentObject.type === \"node\" ? e.selectNode(S(r.currentObject.value)) : r.currentObject.type === \"nodes\" && e.selectNodes(r.currentObject.value.map((c) => S(c)));\n      } catch {\n      } finally {\n        n--;\n      }\n    }\n  }, e.redo = function() {\n    if (n < t.length - 1) {\n      n++;\n      const r = t[n];\n      o = r.next, e.refresh(r.next), r.currentObject.type === \"node\" ? e.selectNode(S(r.currentObject.value)) : r.currentObject.type === \"nodes\" && (e.unselectNodes(this.currentNodes), e.selectNodes(r.currentObject.value.map((c) => S(c))));\n    }\n  };\n  const s = function(r) {\n    if (r.name === \"beginEdit\")\n      return;\n    t = t.slice(0, n + 1);\n    const c = e.getData();\n    t.push({ prev: o, currentObject: Tt(r), next: c }), o = c, n = t.length - 1;\n  }, i = function(r) {\n    (r.metaKey || r.ctrlKey) && (r.shiftKey && r.key === \"Z\" || r.key === \"y\") ? e.redo() : (r.metaKey || r.ctrlKey) && r.key === \"z\" && e.undo();\n  };\n  return e.bus.addListener(\"operation\", s), e.map.addEventListener(\"keydown\", i), () => {\n    e.bus.removeListener(\"operation\", s), e.map.removeEventListener(\"keydown\", i);\n  };\n}\nconst F = (e, t) => {\n  const n = document.createElement(\"span\");\n  return n.id = e, n.innerHTML = `<svg class=\"icon\" aria-hidden=\"true\">\n    <use xlink:href=\"#icon-${t}\"></use>\n  </svg>`, n;\n};\nfunction Lt(e) {\n  const t = document.createElement(\"div\"), n = F(\"fullscreen\", \"full\"), o = F(\"toCenter\", \"living\"), s = F(\"zoomout\", \"move\"), i = F(\"zoomin\", \"add\"), r = document.createElement(\"span\");\n  return r.innerText = \"100%\", t.appendChild(n), t.appendChild(o), t.appendChild(s), t.appendChild(i), t.className = \"mind-elixir-toolbar rb\", n.onclick = () => {\n    e.el.requestFullscreen();\n  }, o.onclick = () => {\n    e.toCenter();\n  }, s.onclick = () => {\n    e.scaleVal < 0.6 || e.scale(e.scaleVal - 0.2);\n  }, i.onclick = () => {\n    e.scaleVal > 1.6 || e.scale(e.scaleVal + 0.2);\n  }, t;\n}\nfunction At(e) {\n  const t = document.createElement(\"div\"), n = F(\"tbltl\", \"left\"), o = F(\"tbltr\", \"right\"), s = F(\"tblts\", \"side\");\n  return t.appendChild(n), t.appendChild(o), t.appendChild(s), t.className = \"mind-elixir-toolbar lt\", n.onclick = () => {\n    e.initLeft();\n  }, o.onclick = () => {\n    e.initRight();\n  }, s.onclick = () => {\n    e.initSide();\n  }, t;\n}\nfunction $t(e) {\n  e.container.append(Lt(e)), e.container.append(At(e));\n}\n/*! @viselect/vanilla v3.5.1 MIT | https://github.com/Simonwep/selection/tree/master/packages/vanilla */\nvar jt = Object.defineProperty, Dt = (e, t, n) => t in e ? jt(e, t, { enumerable: !0, configurable: !0, writable: !0, value: n }) : e[t] = n, C = (e, t, n) => (Dt(e, typeof t != \"symbol\" ? t + \"\" : t, n), n);\nclass Ot {\n  constructor() {\n    C(this, \"_listeners\", /* @__PURE__ */ new Map()), C(this, \"on\", this.addEventListener), C(this, \"off\", this.removeEventListener), C(this, \"emit\", this.dispatchEvent);\n  }\n  addEventListener(t, n) {\n    const o = this._listeners.get(t) ?? /* @__PURE__ */ new Set();\n    return this._listeners.set(t, o), o.add(n), this;\n  }\n  removeEventListener(t, n) {\n    var o;\n    return (o = this._listeners.get(t)) == null || o.delete(n), this;\n  }\n  dispatchEvent(t, ...n) {\n    let o = !0;\n    for (const s of this._listeners.get(t) ?? [])\n      o = s(...n) !== !1 && o;\n    return o;\n  }\n  unbindAllListeners() {\n    this._listeners.clear();\n  }\n}\nconst ke = (e, t = \"px\") => typeof e == \"number\" ? e + t : e;\nfunction D({ style: e }, t, n) {\n  if (typeof t == \"object\")\n    for (const [o, s] of Object.entries(t))\n      s !== void 0 && (e[o] = ke(s));\n  else\n    n !== void 0 && (e[t] = ke(n));\n}\nconst Ye = (e) => (t, n, o, s = {}) => {\n  t instanceof HTMLCollection || t instanceof NodeList ? t = Array.from(t) : Array.isArray(t) || (t = [t]), Array.isArray(n) || (n = [n]);\n  for (const i of t)\n    if (i)\n      for (const r of n)\n        i[e](r, o, { capture: !1, ...s });\n  return [t, n, o, s];\n}, I = Ye(\"addEventListener\"), O = Ye(\"removeEventListener\"), te = (e) => {\n  var t;\n  const { clientX: n, clientY: o, target: s } = ((t = e.touches) == null ? void 0 : t[0]) ?? e;\n  return { x: n, y: o, target: s };\n};\nfunction Le(e, t, n = \"touch\") {\n  switch (n) {\n    case \"center\": {\n      const o = t.left + t.width / 2, s = t.top + t.height / 2;\n      return o >= e.left && o <= e.right && s >= e.top && s <= e.bottom;\n    }\n    case \"cover\":\n      return t.left >= e.left && t.top >= e.top && t.right <= e.right && t.bottom <= e.bottom;\n    case \"touch\":\n      return e.right >= t.left && e.left <= t.right && e.bottom >= t.top && e.top <= t.bottom;\n  }\n}\nfunction K(e, t = document) {\n  const n = Array.isArray(e) ? e : [e];\n  let o = [];\n  for (let s = 0, i = n.length; s < i; s++) {\n    const r = n[s];\n    typeof r == \"string\" ? o = o.concat(Array.from(t.querySelectorAll(r))) : r instanceof Element && o.push(r);\n  }\n  return o;\n}\nconst Ht = () => matchMedia(\"(hover: none), (pointer: coarse)\").matches, Pt = () => \"safari\" in window, Bt = (e) => {\n  let t, n = -1, o = !1;\n  return {\n    next(...s) {\n      t = s, o || (o = !0, n = requestAnimationFrame(() => {\n        e(...t), o = !1;\n      }));\n    },\n    cancel() {\n      cancelAnimationFrame(n), o = !1;\n    }\n  };\n};\nfunction Rt(e, t) {\n  for (const n of t) {\n    if (typeof n == \"number\")\n      return e.button === n;\n    if (typeof n == \"object\") {\n      const o = n.button === e.button, s = n.modifiers.every((i) => {\n        switch (i) {\n          case \"alt\":\n            return e.altKey;\n          case \"ctrl\":\n            return e.ctrlKey || e.metaKey;\n          case \"shift\":\n            return e.shiftKey;\n        }\n      });\n      return o && s;\n    }\n  }\n  return !1;\n}\nconst { abs: z, max: Ae, min: $e, ceil: je } = Math;\nclass We extends Ot {\n  constructor(t) {\n    var n, o, s, i, r;\n    super(), C(this, \"_options\"), C(this, \"_selection\", {\n      stored: [],\n      selected: [],\n      touched: [],\n      changed: {\n        added: [],\n        // Added elements since last selection\n        removed: []\n        // Removed elements since last selection\n      }\n    }), C(this, \"_area\"), C(this, \"_clippingElement\"), C(this, \"_targetElement\"), C(this, \"_targetRect\"), C(this, \"_selectables\", []), C(this, \"_latestElement\"), C(this, \"_areaRect\", new DOMRect()), C(this, \"_areaLocation\", { y1: 0, x2: 0, y2: 0, x1: 0 }), C(this, \"_singleClick\", !0), C(this, \"_frame\"), C(this, \"_scrollAvailable\", !0), C(this, \"_scrollingActive\", !1), C(this, \"_scrollSpeed\", { x: 0, y: 0 }), C(this, \"_scrollDelta\", { x: 0, y: 0 }), C(this, \"disable\", this._bindStartEvents.bind(this, !1)), C(this, \"enable\", this._bindStartEvents), this._options = {\n      selectionAreaClass: \"selection-area\",\n      selectionContainerClass: void 0,\n      selectables: [],\n      document: window.document,\n      startAreas: [\"html\"],\n      boundaries: [\"html\"],\n      container: \"body\",\n      ...t,\n      behaviour: {\n        overlap: \"invert\",\n        intersect: \"touch\",\n        triggers: [0],\n        ...t.behaviour,\n        startThreshold: (n = t.behaviour) != null && n.startThreshold ? typeof t.behaviour.startThreshold == \"number\" ? t.behaviour.startThreshold : { x: 10, y: 10, ...t.behaviour.startThreshold } : { x: 10, y: 10 },\n        scrolling: {\n          speedDivider: 10,\n          manualSpeed: 750,\n          ...(o = t.behaviour) == null ? void 0 : o.scrolling,\n          startScrollMargins: {\n            x: 0,\n            y: 0,\n            ...(i = (s = t.behaviour) == null ? void 0 : s.scrolling) == null ? void 0 : i.startScrollMargins\n          }\n        }\n      },\n      features: {\n        range: !0,\n        touch: !0,\n        ...t.features,\n        singleTap: {\n          allow: !0,\n          intersect: \"native\",\n          ...(r = t.features) == null ? void 0 : r.singleTap\n        }\n      }\n    };\n    for (const l of Object.getOwnPropertyNames(Object.getPrototypeOf(this)))\n      typeof this[l] == \"function\" && (this[l] = this[l].bind(this));\n    const { document: c, selectionAreaClass: a, selectionContainerClass: d } = this._options;\n    this._area = c.createElement(\"div\"), this._clippingElement = c.createElement(\"div\"), this._clippingElement.appendChild(this._area), this._area.classList.add(a), d && this._clippingElement.classList.add(d), D(this._area, {\n      willChange: \"top, left, bottom, right, width, height\",\n      top: 0,\n      left: 0,\n      position: \"fixed\"\n    }), D(this._clippingElement, {\n      overflow: \"hidden\",\n      position: \"fixed\",\n      transform: \"translate3d(0, 0, 0)\",\n      // https://stackoverflow.com/a/38268846\n      pointerEvents: \"none\",\n      zIndex: \"1\"\n    }), this._frame = Bt((l) => {\n      this._recalculateSelectionAreaRect(), this._updateElementSelection(), this._emitEvent(\"move\", l), this._redrawSelectionArea();\n    }), this.enable();\n  }\n  _bindStartEvents(t = !0) {\n    const { document: n, features: o } = this._options, s = t ? I : O;\n    s(n, \"mousedown\", this._onTapStart), o.touch && s(n, \"touchstart\", this._onTapStart, {\n      passive: !1\n    });\n  }\n  _onTapStart(t, n = !1) {\n    const { x: o, y: s, target: i } = te(t), { _options: r } = this, { document: c } = this._options, a = i.getBoundingClientRect();\n    if (t instanceof MouseEvent && !Rt(t, r.behaviour.triggers))\n      return;\n    const d = K(r.startAreas, r.document), l = K(r.boundaries, r.document);\n    this._targetElement = l.find(\n      (v) => Le(v.getBoundingClientRect(), a)\n    );\n    const u = t.composedPath();\n    if (!this._targetElement || !d.find((v) => u.includes(v)) || !l.find((v) => u.includes(v)) || !n && this._emitEvent(\"beforestart\", t) === !1)\n      return;\n    this._areaLocation = { x1: o, y1: s, x2: 0, y2: 0 };\n    const h = c.scrollingElement ?? c.body;\n    this._scrollDelta = { x: h.scrollLeft, y: h.scrollTop }, this._singleClick = !0, this.clearSelection(!1, !0), I(c, [\"touchmove\", \"mousemove\"], this._delayedTapMove, { passive: !1 }), I(c, [\"mouseup\", \"touchcancel\", \"touchend\"], this._onTapStop), I(c, \"scroll\", this._onScroll);\n  }\n  _onSingleTap(t) {\n    const { singleTap: { intersect: n }, range: o } = this._options.features, s = te(t);\n    let i;\n    if (n === \"native\")\n      i = s.target;\n    else if (n === \"touch\") {\n      this.resolveSelectables();\n      const { x: c, y: a } = s;\n      i = this._selectables.find((d) => {\n        const { right: l, left: u, top: h, bottom: v } = d.getBoundingClientRect();\n        return c < l && c > u && a < v && a > h;\n      });\n    }\n    if (!i)\n      return;\n    for (this.resolveSelectables(); !this._selectables.includes(i); ) {\n      if (!i.parentElement)\n        return;\n      i = i.parentElement;\n    }\n    const { stored: r } = this._selection;\n    if (this._emitEvent(\"start\", t), t.shiftKey && o && this._latestElement) {\n      const c = this._latestElement, [a, d] = c.compareDocumentPosition(i) & 4 ? [i, c] : [c, i], l = [...this._selectables.filter(\n        (u) => u.compareDocumentPosition(a) & 4 && u.compareDocumentPosition(d) & 2\n      ), a, d];\n      this.select(l), this._latestElement = c;\n    } else\n      r.includes(i) && (r.length === 1 || t.ctrlKey || r.every((c) => this._selection.stored.includes(c))) ? this.deselect(i) : (this.select(i), this._latestElement = i);\n  }\n  _delayedTapMove(t) {\n    const { container: n, document: o, behaviour: { startThreshold: s } } = this._options, { x1: i, y1: r } = this._areaLocation, { x: c, y: a } = te(t);\n    if (\n      // Single number for both coordinates\n      typeof s == \"number\" && z(c + a - (i + r)) >= s || // Different x and y threshold\n      typeof s == \"object\" && z(c - i) >= s.x || z(a - r) >= s.y\n    ) {\n      if (O(o, [\"mousemove\", \"touchmove\"], this._delayedTapMove, { passive: !1 }), this._emitEvent(\"beforedrag\", t) === !1) {\n        O(o, [\"mouseup\", \"touchcancel\", \"touchend\"], this._onTapStop);\n        return;\n      }\n      I(o, [\"mousemove\", \"touchmove\"], this._onTapMove, { passive: !1 }), D(this._area, \"display\", \"block\"), K(n, o)[0].appendChild(this._clippingElement), this.resolveSelectables(), this._singleClick = !1, this._targetRect = this._targetElement.getBoundingClientRect(), this._scrollAvailable = this._targetElement.scrollHeight !== this._targetElement.clientHeight || this._targetElement.scrollWidth !== this._targetElement.clientWidth, this._scrollAvailable && (I(this._targetElement, \"wheel\", this._manualScroll, { passive: !1 }), this._selectables = this._selectables.filter((d) => this._targetElement.contains(d))), this._setupSelectionArea(), this._emitEvent(\"start\", t), this._onTapMove(t);\n    }\n    this._handleMoveEvent(t);\n  }\n  _setupSelectionArea() {\n    const { _clippingElement: t, _targetElement: n, _area: o } = this, s = this._targetRect = n.getBoundingClientRect();\n    this._scrollAvailable ? (D(t, {\n      top: s.top,\n      left: s.left,\n      width: s.width,\n      height: s.height\n    }), D(o, {\n      marginTop: -s.top,\n      marginLeft: -s.left\n    })) : (D(t, {\n      top: 0,\n      left: 0,\n      width: \"100%\",\n      height: \"100%\"\n    }), D(o, {\n      marginTop: 0,\n      marginLeft: 0\n    }));\n  }\n  _onTapMove(t) {\n    const { x: n, y: o } = te(t), { _scrollSpeed: s, _areaLocation: i, _options: r, _frame: c } = this, { speedDivider: a } = r.behaviour.scrolling, d = this._targetElement;\n    if (i.x2 = n, i.y2 = o, this._scrollAvailable && !this._scrollingActive && (s.y || s.x)) {\n      this._scrollingActive = !0;\n      const l = () => {\n        if (!s.x && !s.y) {\n          this._scrollingActive = !1;\n          return;\n        }\n        const { scrollTop: u, scrollLeft: h } = d;\n        s.y && (d.scrollTop += je(s.y / a), i.y1 -= d.scrollTop - u), s.x && (d.scrollLeft += je(s.x / a), i.x1 -= d.scrollLeft - h), c.next(t), requestAnimationFrame(l);\n      };\n      requestAnimationFrame(l);\n    } else\n      c.next(t);\n    this._handleMoveEvent(t);\n  }\n  _handleMoveEvent(t) {\n    const { features: n } = this._options;\n    (n.touch && Ht() || this._scrollAvailable && Pt()) && t.preventDefault();\n  }\n  _onScroll() {\n    const { _scrollDelta: t, _options: { document: n } } = this, { scrollTop: o, scrollLeft: s } = n.scrollingElement ?? n.body;\n    this._areaLocation.x1 += t.x - s, this._areaLocation.y1 += t.y - o, t.x = s, t.y = o, this._setupSelectionArea(), this._frame.next(null);\n  }\n  _manualScroll(t) {\n    const { manualSpeed: n } = this._options.behaviour.scrolling, o = t.deltaY ? t.deltaY > 0 ? 1 : -1 : 0, s = t.deltaX ? t.deltaX > 0 ? 1 : -1 : 0;\n    this._scrollSpeed.y += o * n, this._scrollSpeed.x += s * n, this._onTapMove(t), t.preventDefault();\n  }\n  _recalculateSelectionAreaRect() {\n    const { _scrollSpeed: t, _areaLocation: n, _areaRect: o, _targetElement: s, _options: i } = this, { scrollTop: r, scrollHeight: c, clientHeight: a, scrollLeft: d, scrollWidth: l, clientWidth: u } = s, h = this._targetRect, { x1: v, y1: f } = n;\n    let { x2: p, y2: m } = n;\n    const { behaviour: { scrolling: { startScrollMargins: g } } } = i;\n    p < h.left + g.x ? (t.x = d ? -z(h.left - p + g.x) : 0, p = p < h.left ? h.left : p) : p > h.right - g.x ? (t.x = l - d - u ? z(h.left + h.width - p - g.x) : 0, p = p > h.right ? h.right : p) : t.x = 0, m < h.top + g.y ? (t.y = r ? -z(h.top - m + g.y) : 0, m = m < h.top ? h.top : m) : m > h.bottom - g.y ? (t.y = c - r - a ? z(h.top + h.height - m - g.y) : 0, m = m > h.bottom ? h.bottom : m) : t.y = 0;\n    const y = $e(v, p), x = $e(f, m), E = Ae(v, p), b = Ae(f, m);\n    o.x = y, o.y = x, o.width = E - y, o.height = b - x;\n  }\n  _redrawSelectionArea() {\n    const { x: t, y: n, width: o, height: s } = this._areaRect, { style: i } = this._area;\n    i.left = `${t}px`, i.top = `${n}px`, i.width = `${o}px`, i.height = `${s}px`;\n  }\n  _onTapStop(t, n) {\n    var o;\n    const { document: s, features: i } = this._options, { _singleClick: r } = this;\n    O(s, [\"mousemove\", \"touchmove\"], this._delayedTapMove), O(s, [\"touchmove\", \"mousemove\"], this._onTapMove), O(s, [\"mouseup\", \"touchcancel\", \"touchend\"], this._onTapStop), O(s, \"scroll\", this._onScroll), this._keepSelection(), t && r && i.singleTap.allow ? this._onSingleTap(t) : !r && !n && (this._updateElementSelection(), this._emitEvent(\"stop\", t)), this._scrollSpeed.x = 0, this._scrollSpeed.y = 0, O(this._targetElement, \"wheel\", this._manualScroll, { passive: !0 }), this._clippingElement.remove(), (o = this._frame) == null || o.cancel(), D(this._area, \"display\", \"none\");\n  }\n  _updateElementSelection() {\n    const { _selectables: t, _options: n, _selection: o, _areaRect: s } = this, { stored: i, selected: r, touched: c } = o, { intersect: a, overlap: d } = n.behaviour, l = d === \"invert\", u = [], h = [], v = [];\n    for (let p = 0; p < t.length; p++) {\n      const m = t[p];\n      if (Le(s, m.getBoundingClientRect(), a)) {\n        if (r.includes(m))\n          i.includes(m) && !c.includes(m) && c.push(m);\n        else if (l && i.includes(m)) {\n          v.push(m);\n          continue;\n        } else\n          h.push(m);\n        u.push(m);\n      }\n    }\n    l && h.push(...i.filter((p) => !r.includes(p)));\n    const f = d === \"keep\";\n    for (let p = 0; p < r.length; p++) {\n      const m = r[p];\n      !u.includes(m) && !// Check if user wants to keep previously selected elements, e.g.\n      // not make them part of the current selection as soon as they're touched.\n      (f && i.includes(m)) && v.push(m);\n    }\n    o.selected = u, o.changed = { added: h, removed: v }, this._latestElement = void 0;\n  }\n  _emitEvent(t, n) {\n    return this.emit(t, {\n      event: n,\n      store: this._selection,\n      selection: this\n    });\n  }\n  _keepSelection() {\n    const { _options: t, _selection: n } = this, { selected: o, changed: s, touched: i, stored: r } = n, c = o.filter((a) => !r.includes(a));\n    switch (t.behaviour.overlap) {\n      case \"drop\": {\n        n.stored = [\n          ...c,\n          ...r.filter((a) => !i.includes(a))\n          // Elements not touched\n        ];\n        break;\n      }\n      case \"invert\": {\n        n.stored = [\n          ...c,\n          ...r.filter((a) => !s.removed.includes(a))\n          // Elements not removed from selection\n        ];\n        break;\n      }\n      case \"keep\": {\n        n.stored = [\n          ...r,\n          ...o.filter((a) => !r.includes(a))\n          // Newly added\n        ];\n        break;\n      }\n    }\n  }\n  /**\n   * Manually triggers the start of a selection\n   * @param evt A MouseEvent / TouchEvent -like object\n   * @param silent If beforestart should be fired,\n   */\n  trigger(t, n = !0) {\n    this._onTapStart(t, n);\n  }\n  /**\n   * Can be used if during a selection elements have been added.\n   * Will update everything which can be selected.\n   */\n  resolveSelectables() {\n    this._selectables = K(this._options.selectables, this._options.document);\n  }\n  /**\n   * Same as deselect, but for all elements currently selected.\n   * @param includeStored If the store should also get cleared\n   * @param quiet If move / stop events should be fired\n   */\n  clearSelection(t = !0, n = !1) {\n    const { selected: o, stored: s, changed: i } = this._selection;\n    i.added = [], i.removed.push(\n      ...o,\n      ...t ? s : []\n    ), n || (this._emitEvent(\"move\", null), this._emitEvent(\"stop\", null)), this._selection = {\n      stored: t ? [] : s,\n      selected: [],\n      touched: [],\n      changed: { added: [], removed: [] }\n    };\n  }\n  /**\n   * @returns {Array} Selected elements\n   */\n  getSelection() {\n    return this._selection.stored;\n  }\n  /**\n   * @returns {HTMLElement} The selection area element\n   */\n  getSelectionArea() {\n    return this._area;\n  }\n  /**\n   * Cancel the current selection process.\n   * @param keepEvent {boolean} true to fire a stop event after cancel.\n   */\n  cancel(t = !1) {\n    this._onTapStop(null, !t);\n  }\n  /**\n   * Unbinds all events and removes the area-element.\n   */\n  destroy() {\n    this.cancel(), this.disable(), this._clippingElement.remove(), super.unbindAllListeners();\n  }\n  /**\n   * Adds elements to the selection\n   * @param query - CSS Query, can be an array of queries\n   * @param quiet - If this should not trigger the move event\n   */\n  select(t, n = !1) {\n    const { changed: o, selected: s, stored: i } = this._selection, r = K(t, this._options.document).filter(\n      (c) => !s.includes(c) && !i.includes(c)\n    );\n    return i.push(...r), s.push(...r), o.added.push(...r), o.removed = [], this._latestElement = void 0, n || (this._emitEvent(\"move\", null), this._emitEvent(\"stop\", null)), r;\n  }\n  /**\n   * Removes a particular element from the selection.\n   * @param query - CSS Query, can be an array of queries\n   * @param quiet - If this should not trigger the move event\n   */\n  deselect(t, n = !1) {\n    const { selected: o, stored: s, changed: i } = this._selection, r = K(t, this._options.document).filter(\n      (c) => o.includes(c) || s.includes(c)\n    );\n    r.length && (this._selection.stored = s.filter((c) => !r.includes(c)), this._selection.selected = o.filter((c) => !r.includes(c)), this._selection.changed.added = [], this._selection.changed.removed.push(\n      ...r.filter((c) => !i.removed.includes(c))\n    ), this._latestElement = void 0, n || (this._emitEvent(\"move\", null), this._emitEvent(\"stop\", null)));\n  }\n}\nC(We, \"version\", \"3.5.1\");\nfunction zt(e) {\n  const t = e.mouseSelectionButton === 2 ? [2] : [0], n = new We({\n    selectables: [\".map-container me-tpc\"],\n    boundaries: [e.container],\n    container: e.selectionContainer,\n    features: {\n      // deselectOnBlur: true,\n      touch: !1\n    },\n    behaviour: {\n      triggers: t,\n      // Scroll configuration.\n      scrolling: {\n        // On scrollable areas the number on px per frame is devided by this amount.\n        // Default is 10 to provide a enjoyable scroll experience.\n        speedDivider: 10,\n        // Browsers handle mouse-wheel events differently, this number will be used as\n        // numerator to calculate the mount of px while scrolling manually: manualScrollSpeed / scrollSpeedDivider.\n        manualSpeed: 750,\n        // This property defines the virtual inset margins from the borders of the container\n        // component that, when crossed by the mouse/touch, trigger the scrolling. Useful for\n        // fullscreen containers.\n        startScrollMargins: { x: 10, y: 10 }\n      }\n    }\n  }).on(\"beforestart\", ({ event: o }) => {\n    const s = o.target;\n    if (s.id === \"input-box\" || s.className === \"circle\" || !e.map.contains(s))\n      return !1;\n    if (!o.ctrlKey && !o.metaKey) {\n      if (s.tagName === \"ME-TPC\" && s.classList.contains(\"selected\"))\n        return !1;\n      e.clearSelection();\n    }\n    const i = n.getSelectionArea();\n    return i.style.background = \"#4f90f22d\", i.style.border = \"1px solid #4f90f2\", i.parentElement && (i.parentElement.style.zIndex = \"9999\"), !0;\n  }).on(\n    \"move\",\n    ({\n      store: {\n        changed: { added: o, removed: s }\n      }\n    }) => {\n      if (o.length > 0 || s.length > 0, o.length > 0) {\n        for (const i of o)\n          i.className = \"selected\";\n        e.currentNodes = [...e.currentNodes, ...o], e.bus.fire(\n          \"selectNodes\",\n          o.map((i) => i.nodeObj)\n        );\n      }\n      if (s.length > 0) {\n        for (const i of s)\n          i.classList.remove(\"selected\");\n        e.currentNodes = e.currentNodes.filter((i) => !(s != null && s.includes(i))), e.bus.fire(\n          \"unselectNodes\",\n          s.map((i) => i.nodeObj)\n        );\n      }\n    }\n  );\n  e.selection = n;\n}\nconst Ft = function(e, t = !0) {\n  this.theme = e;\n  const n = this.theme.cssVar, o = Object.keys(n);\n  this.container.style.cssText = \"\";\n  for (let s = 0; s < o.length; s++) {\n    const i = o[s];\n    this.container.style.setProperty(i, n[i]);\n  }\n  e.cssVar[\"--gap\"] || this.container.style.setProperty(\"--gap\", \"30px\"), t && this.refresh();\n}, X = (e) => {\n  var o;\n  const t = (o = e.parent) == null ? void 0 : o.children, n = (t == null ? void 0 : t.indexOf(e)) ?? 0;\n  return { siblings: t, index: n };\n};\nfunction qt(e) {\n  const { siblings: t, index: n } = X(e);\n  if (t === void 0)\n    return;\n  const o = t[n];\n  n === 0 ? (t[n] = t[t.length - 1], t[t.length - 1] = o) : (t[n] = t[n - 1], t[n - 1] = o);\n}\nfunction Vt(e) {\n  const { siblings: t, index: n } = X(e);\n  if (t === void 0)\n    return;\n  const o = t[n];\n  n === t.length - 1 ? (t[n] = t[0], t[0] = o) : (t[n] = t[n + 1], t[n + 1] = o);\n}\nfunction Xe(e) {\n  const { siblings: t, index: n } = X(e);\n  return t === void 0 ? 0 : (t.splice(n, 1), t.length);\n}\nfunction It(e, t, n) {\n  const { siblings: o, index: s } = X(n);\n  o !== void 0 && (t === \"before\" ? o.splice(s, 0, e) : o.splice(s + 1, 0, e));\n}\nfunction Kt(e, t) {\n  const { siblings: n, index: o } = X(e);\n  n !== void 0 && (n[o] = t, t.children = [e]);\n}\nfunction Ue(e, t, n) {\n  var o;\n  if (Xe(t), (o = n.parent) != null && o.parent || (t.direction = n.direction), e === \"in\")\n    n.children ? n.children.push(t) : n.children = [t];\n  else {\n    t.direction !== void 0 && (t.direction = n.direction);\n    const { siblings: s, index: i } = X(n);\n    if (s === void 0)\n      return;\n    e === \"before\" ? s.splice(i, 0, t) : s.splice(i + 1, 0, t);\n  }\n}\nconst Yt = function({ map: e, direction: t }, n) {\n  var o, s;\n  if (t === A)\n    return A;\n  if (t === H)\n    return H;\n  if (t === ae) {\n    const i = ((o = e.querySelector(\".lhs\")) == null ? void 0 : o.childElementCount) || 0, r = ((s = e.querySelector(\".rhs\")) == null ? void 0 : s.childElementCount) || 0;\n    return i <= r ? (n.direction = A, A) : (n.direction = H, H);\n  }\n}, Ge = function(e, t, n) {\n  var i, r;\n  const o = n.children[0].children[0], s = t.parentElement;\n  if (s.tagName === \"ME-PARENT\") {\n    if (Q(o), s.children[1])\n      s.nextSibling.appendChild(n);\n    else {\n      const c = e.createChildren([n]);\n      s.appendChild(ve(!0)), s.insertAdjacentElement(\"afterend\", c);\n    }\n    e.linkDiv(n.offsetParent);\n  } else\n    s.tagName === \"ME-ROOT\" && (Yt(e, o.nodeObj) === A ? (i = e.container.querySelector(\".lhs\")) == null || i.appendChild(n) : (r = e.container.querySelector(\".rhs\")) == null || r.appendChild(n), e.linkDiv());\n}, Wt = function(e, t) {\n  const n = e.parentNode;\n  if (t === 0) {\n    const o = n.parentNode.parentNode;\n    o.tagName !== \"ME-MAIN\" && (o.previousSibling.children[1].remove(), o.remove());\n  }\n  n.parentNode.remove();\n}, Je = {\n  before: \"beforebegin\",\n  after: \"afterend\"\n}, Q = function(e) {\n  const n = e.parentElement.parentElement.lastElementChild;\n  (n == null ? void 0 : n.tagName) === \"svg\" && (n == null || n.remove());\n}, Xt = function(e, t) {\n  const n = e.nodeObj, o = pe(n);\n  o.style && t.style && (t.style = Object.assign(o.style, t.style));\n  const s = Object.assign(n, t);\n  ge(e, s), this.linkDiv(), this.bus.fire(\"operation\", {\n    name: \"reshapeNode\",\n    obj: s,\n    origin: o\n  });\n}, ye = function(e, t, n) {\n  if (!t)\n    return null;\n  const o = t.nodeObj;\n  o.expanded === !1 && (e.expandNode(t, !0), t = S(o.id));\n  const s = n || e.generateNewObj();\n  o.children ? o.children.push(s) : o.children = [s], P(e.nodeData);\n  const { grp: i, top: r } = e.createWrapper(s);\n  return Ge(e, t, i), { newTop: r, newNodeObj: s };\n}, Ut = function(e, t, n) {\n  var d, l, u, h;\n  const o = t || this.currentNode;\n  if (!o)\n    return;\n  const s = o.nodeObj;\n  if (s.parent) {\n    if (!((d = s.parent) != null && d.parent) && ((u = (l = s.parent) == null ? void 0 : l.children) == null ? void 0 : u.length) === 1) {\n      this.addChild(S(s.parent.id), n);\n      return;\n    }\n  } else {\n    this.addChild();\n    return;\n  }\n  const i = n || this.generateNewObj();\n  if (!((h = s.parent) != null && h.parent)) {\n    const v = o.closest(\"me-main\").className === M.LHS ? A : H;\n    i.direction = v;\n  }\n  It(i, e, s), P(this.nodeData);\n  const r = o.parentElement, { grp: c, top: a } = this.createWrapper(i);\n  r.parentElement.insertAdjacentElement(Je[e], c), this.linkDiv(c.offsetParent), n || this.editTopic(a.firstChild), this.selectNode(a.firstChild, !0), this.bus.fire(\"operation\", {\n    name: \"insertSibling\",\n    type: e,\n    obj: i\n  });\n}, Gt = function(e, t) {\n  const n = e || this.currentNode;\n  if (!n)\n    return;\n  Q(n);\n  const o = n.nodeObj;\n  if (!o.parent)\n    return;\n  const s = t || this.generateNewObj();\n  Kt(o, s), P(this.nodeData);\n  const i = n.parentElement.parentElement, { grp: r, top: c } = this.createWrapper(s, !0);\n  c.appendChild(ve(!0)), i.insertAdjacentElement(\"afterend\", r);\n  const a = this.createChildren([i]);\n  c.insertAdjacentElement(\"afterend\", a), this.linkDiv(), t || this.editTopic(c.firstChild), this.selectNode(c.firstChild, !0), this.bus.fire(\"operation\", {\n    name: \"insertParent\",\n    obj: s\n  });\n}, Jt = function(e, t) {\n  const n = e || this.currentNode;\n  if (!n)\n    return;\n  const o = ye(this, n, t);\n  if (!o)\n    return;\n  const { newTop: s, newNodeObj: i } = o;\n  this.bus.fire(\"operation\", {\n    name: \"addChild\",\n    obj: i\n  }), t || this.editTopic(s.firstChild), this.selectNode(s.firstChild, !0);\n}, Zt = function(e, t) {\n  const n = pe(e.nodeObj);\n  fe(n);\n  const o = ye(this, t, n);\n  if (!o)\n    return;\n  const { newNodeObj: s } = o;\n  this.selectNode(S(s.id)), this.bus.fire(\"operation\", {\n    name: \"copyNode\",\n    obj: s\n  });\n}, Qt = function(e, t) {\n  e = de(e);\n  const n = [];\n  for (let o = 0; o < e.length; o++) {\n    const s = e[o], i = pe(s.nodeObj);\n    fe(i);\n    const r = ye(this, t, i);\n    if (!r)\n      return;\n    const { newNodeObj: c } = r;\n    n.push(c);\n  }\n  this.unselectNodes(this.currentNodes), this.selectNodes(n.map((o) => S(o.id))), this.bus.fire(\"operation\", {\n    name: \"copyNodes\",\n    objs: n\n  });\n}, en = function(e) {\n  const t = e || this.currentNode;\n  if (!t)\n    return;\n  const n = t.nodeObj;\n  qt(n);\n  const o = t.parentNode.parentNode;\n  o.parentNode.insertBefore(o, o.previousSibling), this.linkDiv(), this.bus.fire(\"operation\", {\n    name: \"moveUpNode\",\n    obj: n\n  });\n}, tn = function(e) {\n  const t = e || this.currentNode;\n  if (!t)\n    return;\n  const n = t.nodeObj;\n  Vt(n);\n  const o = t.parentNode.parentNode;\n  o.nextSibling ? o.nextSibling.insertAdjacentElement(\"afterend\", o) : o.parentNode.prepend(o), this.linkDiv(), this.bus.fire(\"operation\", {\n    name: \"moveDownNode\",\n    obj: n\n  });\n}, nn = function(e) {\n  if (e.length === 0)\n    return;\n  e = de(e);\n  for (const n of e) {\n    const o = n.nodeObj, s = Xe(o);\n    Wt(n, s);\n  }\n  const t = e[e.length - 1];\n  this.selectNode(S(t.nodeObj.parent.id)), this.linkDiv(), this.bus.fire(\"operation\", {\n    name: \"removeNodes\",\n    objs: e.map((n) => n.nodeObj)\n  });\n}, on = function(e, t) {\n  e = de(e);\n  const n = t.nodeObj;\n  n.expanded === !1 && (this.expandNode(t, !0), t = S(n.id));\n  for (const o of e) {\n    const s = o.nodeObj;\n    Ue(\"in\", s, n), P(this.nodeData);\n    const i = o.parentElement;\n    Ge(this, t, i.parentElement);\n  }\n  this.linkDiv(), this.bus.fire(\"operation\", {\n    name: \"moveNodeIn\",\n    objs: e.map((o) => o.nodeObj),\n    toObj: n\n  });\n}, Ze = (e, t, n, o) => {\n  e = de(e), t === \"after\" && (e = e.reverse());\n  const s = n.nodeObj, i = [];\n  for (const r of e) {\n    const c = r.nodeObj;\n    Ue(t, c, s), P(o.nodeData), Q(r);\n    const a = r.parentElement.parentNode;\n    i.includes(a.parentElement) || i.push(a.parentElement), n.parentElement.parentNode.insertAdjacentElement(Je[t], a);\n  }\n  for (const r of i)\n    r.childElementCount === 0 && r.tagName !== \"ME-MAIN\" && (r.previousSibling.children[1].remove(), r.remove());\n  o.linkDiv(), o.bus.fire(\"operation\", {\n    name: t === \"before\" ? \"moveNodeBefore\" : \"moveNodeAfter\",\n    objs: e.map((r) => r.nodeObj),\n    toObj: s\n  });\n}, sn = function(e, t) {\n  Ze(e, \"before\", t, this);\n}, rn = function(e, t) {\n  Ze(e, \"after\", t, this);\n}, cn = function(e) {\n  const t = e || this.currentNode;\n  t && (t.nodeObj.dangerouslySetInnerHTML || this.editTopic(t));\n}, ln = function(e, t) {\n  e.text.textContent = t, e.nodeObj.topic = t, this.linkDiv();\n}, Qe = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({\n  __proto__: null,\n  addChild: Jt,\n  beginEdit: cn,\n  copyNode: Zt,\n  copyNodes: Qt,\n  insertParent: Gt,\n  insertSibling: Ut,\n  moveDownNode: tn,\n  moveNodeAfter: rn,\n  moveNodeBefore: sn,\n  moveNodeIn: on,\n  moveUpNode: en,\n  removeNodes: nn,\n  reshapeNode: Xt,\n  rmSubline: Q,\n  setNodeTopic: ln\n}, Symbol.toStringTag, { value: \"Module\" }));\nfunction an(e) {\n  return {\n    nodeData: e.isFocusMode ? e.nodeDataBackup : e.nodeData,\n    arrows: e.arrows,\n    summaries: e.summaries,\n    direction: e.direction,\n    theme: e.theme\n  };\n}\nconst dn = function(e) {\n  const t = this.container, n = e.getBoundingClientRect(), o = t.getBoundingClientRect();\n  if (n.bottom > o.bottom || n.top < o.top || n.right > o.right || n.left < o.left) {\n    const i = n.left + n.width / 2, r = n.top + n.height / 2, c = o.left + o.width / 2, a = o.top + o.height / 2, d = i - c, l = r - a;\n    this.move(-d, -l);\n  }\n}, hn = function(e, t, n) {\n  this.clearSelection(), this.scrollIntoView(e), this.selection.select(e), t && this.bus.fire(\"selectNewNode\", e.nodeObj);\n}, un = function(e) {\n  this.selection.select(e);\n}, fn = function(e) {\n  this.selection.deselect(e);\n}, pn = function() {\n  this.unselectNodes(this.currentNodes), this.unselectSummary(), this.unselectArrow();\n}, mn = function() {\n  const e = an(this);\n  return JSON.stringify(e, (t, n) => {\n    if (!(t === \"parent\" && typeof n != \"string\"))\n      return n;\n  });\n}, gn = function() {\n  return JSON.parse(this.getDataString());\n}, vn = function() {\n  this.editable = !0;\n}, yn = function() {\n  this.editable = !1;\n}, bn = function(e, t = { x: 0, y: 0 }) {\n  const n = this.container.getBoundingClientRect(), o = n.width / 2 - 1e4, s = n.height / 2 - 1e4, i = this.scaleVal, r = this.map.style.transform, { x: c, y: a } = Fe(r), d = c - (t.x - n.left - n.width / 2), l = a - (t.y - n.top - n.height / 2), u = (o - d) * (1 - e / i), h = (s - l) * (1 - e / i);\n  this.map.style.transform = `translate(${c + u}px, ${a + h}px) scale(${e})`, this.scaleVal = e, this.bus.fire(\"scale\", e);\n}, xn = function() {\n  const e = this.nodes.offsetHeight / this.container.offsetHeight, t = this.nodes.offsetWidth / this.container.offsetWidth, n = 1 / Math.max(1, Math.max(e, t));\n  this.scaleVal = n, this.map.style.transform = \"scale(\" + n + \")\", this.bus.fire(\"scale\", n);\n}, wn = function(e, t) {\n  const { map: n, scaleVal: o, container: s, bus: i } = this, r = n.style.transform, { x: c, y: a } = Fe(r), d = c + e, l = a + t, u = (1 - o) * 1e4, h = 0 - u, v = -2e4 + s.offsetWidth + u, f = -2e4 + s.offsetHeight + u, p = Math.min(h, Math.max(v, d)), m = Math.min(h, Math.max(f, l));\n  n.style.transform = `translate(${p}px, ${m}px) scale(${o})`, i.fire(\"move\", { dx: e, dy: t });\n}, En = function() {\n  const e = this.container.getBoundingClientRect(), t = e.width / 2 - 1e4, n = e.height / 2 - 1e4;\n  this.map.style.transform = `translate(${t}px, ${n}px) scale(${this.scaleVal})`;\n}, Cn = function(e) {\n  e(this);\n}, Nn = function(e) {\n  e.nodeObj.parent && (this.clearSelection(), this.tempDirection === null && (this.tempDirection = this.direction), this.isFocusMode || (this.nodeDataBackup = this.nodeData, this.isFocusMode = !0), this.nodeData = e.nodeObj, this.initRight(), this.toCenter());\n}, Sn = function() {\n  this.isFocusMode = !1, this.tempDirection !== null && (this.nodeData = this.nodeDataBackup, this.direction = this.tempDirection, this.tempDirection = null, this.refresh(), this.toCenter());\n}, _n = function() {\n  this.direction = 0, this.refresh();\n}, Mn = function() {\n  this.direction = 1, this.refresh();\n}, Tn = function() {\n  this.direction = 2, this.refresh();\n}, kn = function(e) {\n  this.locale = e, this.refresh();\n}, Ln = function(e, t) {\n  const n = e.nodeObj;\n  typeof t == \"boolean\" ? n.expanded = t : n.expanded !== !1 ? n.expanded = !1 : n.expanded = !0;\n  const o = e.parentNode, s = o.children[1];\n  if (s.expanded = n.expanded, s.className = n.expanded ? \"minus\" : \"\", Q(e), n.expanded) {\n    const a = this.createChildren(\n      n.children.map((d) => this.createWrapper(d).grp)\n    );\n    o.parentNode.appendChild(a);\n  } else\n    o.parentNode.children[1].remove();\n  this.linkDiv(e.closest(\"me-main > me-wrapper\"));\n  const i = e.getBoundingClientRect(), r = this.container.getBoundingClientRect();\n  (i.bottom > r.bottom || i.top < r.top || i.right > r.right || i.left < r.left) && this.scrollIntoView(e), this.bus.fire(\"expandNode\", n);\n}, An = function(e, t) {\n  const n = e.nodeObj, o = (s, i) => {\n    s.expanded = i, s.children && s.children.forEach((r) => {\n      o(r, i);\n    });\n  };\n  typeof t == \"boolean\" ? o(n, t) : n.expanded !== !1 ? o(n, !1) : o(n, !0), this.refresh();\n}, $n = function(e) {\n  e && (e = JSON.parse(JSON.stringify(e)), this.nodeData = e.nodeData, this.arrows = e.arrows || [], this.summaries = e.summaries || []), P(this.nodeData), this.layout(), this.linkDiv();\n}, jn = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({\n  __proto__: null,\n  cancelFocus: Sn,\n  clearSelection: pn,\n  disableEdit: yn,\n  enableEdit: vn,\n  expandNode: Ln,\n  expandNodeAll: An,\n  focusNode: Nn,\n  getData: gn,\n  getDataString: mn,\n  initLeft: _n,\n  initRight: Mn,\n  initSide: Tn,\n  install: Cn,\n  move: wn,\n  refresh: $n,\n  scale: bn,\n  scaleFit: xn,\n  scrollIntoView: dn,\n  selectNode: hn,\n  selectNodes: un,\n  setLocale: kn,\n  toCenter: En,\n  unselectNodes: fn\n}, Symbol.toStringTag, { value: \"Module\" })), Dn = function(e) {\n  return {\n    dom: e,\n    moved: !1,\n    // diffrentiate click and move\n    mousedown: !1,\n    handleMouseMove(t) {\n      this.mousedown && (this.moved = !0, this.cb && this.cb(t.movementX, t.movementY));\n    },\n    handleMouseDown(t) {\n      t.button === 0 && (this.mousedown = !0);\n    },\n    handleClear(t) {\n      this.mousedown = !1;\n    },\n    cb: null,\n    init(t, n) {\n      this.cb = n, this.handleClear = this.handleClear.bind(this), this.handleMouseMove = this.handleMouseMove.bind(this), this.handleMouseDown = this.handleMouseDown.bind(this), this.destroy = me([\n        { dom: t, evt: \"mousemove\", func: this.handleMouseMove },\n        { dom: t, evt: \"mouseleave\", func: this.handleClear },\n        { dom: t, evt: \"mouseup\", func: this.handleClear },\n        { dom: this.dom, evt: \"mousedown\", func: this.handleMouseDown }\n      ]);\n    },\n    destroy: null,\n    clear() {\n      this.moved = !1, this.mousedown = !1;\n    }\n  };\n}, De = {\n  create: Dn\n};\nfunction le(e, t, n) {\n  const { offsetLeft: o, offsetTop: s } = B(e.nodes, t), i = t.offsetWidth, r = t.offsetHeight, c = o + i / 2, a = s + r / 2, d = c + n.x, l = a + n.y;\n  return {\n    w: i,\n    h: r,\n    cx: c,\n    cy: a,\n    ctrlX: d,\n    ctrlY: l\n  };\n}\nfunction Y(e) {\n  let t, n;\n  const o = (e.cy - e.ctrlY) / (e.ctrlX - e.cx);\n  return o > e.h / e.w || o < -e.h / e.w ? e.cy - e.ctrlY < 0 ? (t = e.cx - e.h / 2 / o, n = e.cy + e.h / 2) : (t = e.cx + e.h / 2 / o, n = e.cy - e.h / 2) : e.cx - e.ctrlX < 0 ? (t = e.cx + e.w / 2, n = e.cy - e.w * o / 2) : (t = e.cx - e.w / 2, n = e.cy + e.w * o / 2), {\n    x: t,\n    y: n\n  };\n}\nconst On = function(e, t, n, o) {\n  const s = document.createElementNS(\"http://www.w3.org/2000/svg\", \"text\");\n  return N(s, {\n    \"text-anchor\": \"middle\",\n    x: t + \"\",\n    y: n + \"\",\n    fill: o || \"#666\"\n  }), s.dataset.type = \"custom-link\", s.innerHTML = e, s;\n}, be = function(e, t, n, o, s) {\n  if (!t || !n)\n    return;\n  performance.now();\n  const i = le(e, t, o.delta1), r = le(e, n, o.delta2), { x: c, y: a } = Y(i), { ctrlX: d, ctrlY: l } = i, { ctrlX: u, ctrlY: h } = r, { x: v, y: f } = Y(r), p = re(u, h, v, f);\n  if (!p)\n    return;\n  const m = `M ${p.x1} ${p.y1} L ${v} ${f} L ${p.x2} ${p.y2}`;\n  let g = \"\";\n  if (o.bidirectional) {\n    const w = re(d, l, c, a);\n    if (!w)\n      return;\n    g = `M ${w.x1} ${w.y1} L ${c} ${a} L ${w.x2} ${w.y2}`;\n  }\n  const y = mt(`M ${c} ${a} C ${d} ${l} ${u} ${h} ${v} ${f}`, m, g), x = c / 8 + d * 3 / 8 + u * 3 / 8 + v / 8, E = a / 8 + l * 3 / 8 + h * 3 / 8 + f / 8, b = On(o.label, x, E, e.theme.cssVar[\"--color\"]);\n  y.appendChild(b), y.label = b, y.arrowObj = o, y.dataset.linkid = o.id, e.linkSvgGroup.appendChild(y), s || (e.arrows.push(o), e.currentArrow = y, et(e, o, i, r)), performance.now();\n}, Hn = function(e, t, n = {}) {\n  const o = {\n    id: V(),\n    label: \"Custom Link\",\n    from: e.nodeObj.id,\n    to: t.nodeObj.id,\n    delta1: {\n      x: e.offsetWidth / 2 + 100,\n      y: 0\n    },\n    delta2: {\n      x: t.offsetWidth / 2 + 100,\n      y: 0\n    },\n    ...n\n  };\n  be(this, e, t, o), this.bus.fire(\"operation\", {\n    name: \"createArrow\",\n    obj: o\n  });\n}, Pn = function(e) {\n  const t = { ...e, id: V() };\n  be(this, S(t.from), S(t.to), t), this.bus.fire(\"operation\", {\n    name: \"createArrow\",\n    obj: t\n  });\n}, Bn = function(e) {\n  let t;\n  if (e ? t = e : t = this.currentArrow, !t)\n    return;\n  xe(this);\n  const n = t.arrowObj.id;\n  this.arrows = this.arrows.filter((o) => o.id !== n), t.remove(), this.bus.fire(\"operation\", {\n    name: \"removeArrow\",\n    obj: {\n      id: n\n    }\n  });\n}, Rn = function(e) {\n  this.currentArrow = e;\n  const t = e.arrowObj, n = S(t.from), o = S(t.to), s = le(this, n, t.delta1), i = le(this, o, t.delta2);\n  et(this, t, s, i);\n}, zn = function() {\n  this.currentArrow = null, xe(this);\n}, xe = function(e) {\n  var t, n;\n  (t = e.helper1) == null || t.destroy(), (n = e.helper2) == null || n.destroy(), e.linkController.style.display = \"none\", e.P2.style.display = \"none\", e.P3.style.display = \"none\";\n}, et = function(e, t, n, o) {\n  e.linkController.style.display = \"initial\", e.P2.style.display = \"initial\", e.P3.style.display = \"initial\", e.nodes.appendChild(e.linkController), e.nodes.appendChild(e.P2), e.nodes.appendChild(e.P3);\n  let { x: s, y: i } = Y(n), { ctrlX: r, ctrlY: c } = n, { ctrlX: a, ctrlY: d } = o, { x: l, y: u } = Y(o);\n  e.P2.style.cssText = `top:${c}px;left:${r}px;`, e.P3.style.cssText = `top:${d}px;left:${a}px;`, N(e.line1, {\n    x1: s + \"\",\n    y1: i + \"\",\n    x2: r + \"\",\n    y2: c + \"\"\n  }), N(e.line2, {\n    x1: a + \"\",\n    y1: d + \"\",\n    x2: l + \"\",\n    y2: u + \"\"\n  }), e.helper1 = De.create(e.P2), e.helper2 = De.create(e.P3), e.helper1.init(e.map, (h, v) => {\n    if (!e.currentArrow)\n      return;\n    r = r + h / e.scaleVal, c = c + v / e.scaleVal;\n    const f = Y({ ...n, ctrlX: r, ctrlY: c });\n    s = f.x, i = f.y;\n    const p = s / 8 + r * 3 / 8 + a * 3 / 8 + l / 8, m = i / 8 + c * 3 / 8 + d * 3 / 8 + u / 8;\n    if (e.P2.style.top = c + \"px\", e.P2.style.left = r + \"px\", e.currentArrow.line.setAttribute(\"d\", `M ${s} ${i} C ${r} ${c} ${a} ${d} ${l} ${u}`), t.bidirectional) {\n      const g = re(r, c, s, i);\n      if (!g)\n        return;\n      e.currentArrow.arrow2.setAttribute(\"d\", `M ${g.x1} ${g.y1} L ${s} ${i} L ${g.x2} ${g.y2}`);\n    }\n    N(e.currentArrow.label, {\n      x: p + \"\",\n      y: m + \"\"\n    }), N(e.line1, {\n      x1: s + \"\",\n      y1: i + \"\",\n      x2: r + \"\",\n      y2: c + \"\"\n    }), t.delta1.x = r - n.cx, t.delta1.y = c - n.cy, e.bus.fire(\"updateArrowDelta\", t);\n  }), e.helper2.init(e.map, (h, v) => {\n    if (!e.currentArrow)\n      return;\n    a = a + h / e.scaleVal, d = d + v / e.scaleVal;\n    const f = Y({ ...o, ctrlX: a, ctrlY: d });\n    l = f.x, u = f.y;\n    const p = s / 8 + r * 3 / 8 + a * 3 / 8 + l / 8, m = i / 8 + c * 3 / 8 + d * 3 / 8 + u / 8, g = re(a, d, l, u);\n    g && (e.P3.style.top = d + \"px\", e.P3.style.left = a + \"px\", e.currentArrow.line.setAttribute(\"d\", `M ${s} ${i} C ${r} ${c} ${a} ${d} ${l} ${u}`), e.currentArrow.arrow1.setAttribute(\"d\", `M ${g.x1} ${g.y1} L ${l} ${u} L ${g.x2} ${g.y2}`), N(e.currentArrow.label, {\n      x: p + \"\",\n      y: m + \"\"\n    }), N(e.line2, {\n      x1: a + \"\",\n      y1: d + \"\",\n      x2: l + \"\",\n      y2: u + \"\"\n    }), t.delta2.x = a - o.cx, t.delta2.y = d - o.cy, e.bus.fire(\"updateArrowDelta\", t));\n  });\n};\nfunction Fn() {\n  this.linkSvgGroup.innerHTML = \"\";\n  for (let e = 0; e < this.arrows.length; e++) {\n    const t = this.arrows[e];\n    try {\n      be(this, S(t.from), S(t.to), t, !0);\n    } catch {\n    }\n  }\n  this.nodes.appendChild(this.linkSvgGroup);\n}\nfunction qn(e) {\n  if (xe(this), !e)\n    return;\n  const t = e.label;\n  Ie(this, t, e.arrowObj);\n}\nfunction Vn() {\n  this.arrows = this.arrows.filter((e) => ie(e.from, this.nodeData) && ie(e.to, this.nodeData));\n}\nconst In = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({\n  __proto__: null,\n  createArrow: Hn,\n  createArrowFrom: Pn,\n  editArrowLabel: qn,\n  removeArrow: Bn,\n  renderArrow: Fn,\n  selectArrow: Rn,\n  tidyArrow: Vn,\n  unselectArrow: zn\n}, Symbol.toStringTag, { value: \"Module\" })), Kn = function(e) {\n  var a, d;\n  if (e.length === 0)\n    throw new Error(\"No selected node.\");\n  if (e.length === 1) {\n    const l = e[0].nodeObj, u = e[0].nodeObj.parent;\n    if (!u)\n      throw new Error(\"Can not select root node.\");\n    const h = u.children.findIndex((v) => l === v);\n    return {\n      parent: u.id,\n      start: h,\n      end: h\n    };\n  }\n  let t = 0;\n  const n = e.map((l) => {\n    let u = l.nodeObj;\n    const h = [];\n    for (; u.parent; ) {\n      const v = u.parent, f = v.children, p = f == null ? void 0 : f.indexOf(u);\n      u = v, h.unshift({ node: u, index: p });\n    }\n    return h.length > t && (t = h.length), h;\n  });\n  let o = 0;\n  e:\n    for (; o < t; o++) {\n      const l = (a = n[0][o]) == null ? void 0 : a.node;\n      for (let u = 1; u < n.length; u++)\n        if (((d = n[u][o]) == null ? void 0 : d.node) !== l)\n          break e;\n    }\n  if (!o)\n    throw new Error(\"Can not select root node.\");\n  const s = n.map((l) => l[o - 1].index).sort(), i = s[0] || 0, r = s[s.length - 1] || 0, c = n[0][o - 1].node;\n  if (!c.parent)\n    throw new Error(\"Please select nodes in the same main topic.\");\n  return {\n    parent: c.id,\n    start: i,\n    end: r\n  };\n}, Yn = function(e) {\n  const t = document.createElementNS(\"http://www.w3.org/2000/svg\", \"g\");\n  return t.setAttribute(\"id\", e), t;\n}, Oe = function(e, t) {\n  const n = document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\");\n  return N(n, {\n    d: e,\n    stroke: t || \"#666\",\n    fill: \"none\",\n    \"stroke-linecap\": \"round\",\n    \"stroke-width\": \"2\"\n  }), n;\n}, He = function(e, t, n, o, s) {\n  const i = document.createElementNS(\"http://www.w3.org/2000/svg\", \"text\");\n  return N(i, {\n    \"text-anchor\": o,\n    x: t + \"\",\n    y: n + \"\",\n    fill: s || \"#666\"\n  }), i.innerHTML = e, i;\n}, Wn = (e) => S(e).parentElement.parentElement, Xn = function({ parent: e, start: t }) {\n  const n = S(e), o = n.nodeObj;\n  let s;\n  return o.parent ? s = n.closest(\"me-main\").className : s = S(o.children[t].id).closest(\"me-main\").className, s;\n}, we = function(e, t) {\n  var w;\n  const { id: n, label: o, parent: s, start: i, end: r } = t, c = e.nodes, d = S(s).nodeObj, l = Xn(t);\n  let u = 1 / 0, h = 0, v = 0, f = 0;\n  for (let _ = i; _ <= r; _++) {\n    const T = (w = d.children) == null ? void 0 : w[_];\n    if (!T)\n      return e.removeSummary(n), null;\n    const $ = Wn(T.id), { offsetLeft: R, offsetTop: U } = B(c, $), Ee = i === r ? 10 : 20;\n    _ === i && (v = U + Ee), _ === r && (f = U + $.offsetHeight - Ee), R < u && (u = R), $.offsetWidth + R > h && (h = $.offsetWidth + R);\n  }\n  let p, m;\n  const g = v + 10, y = f + 10, x = (g + y) / 2, E = e.theme.cssVar[\"--color\"];\n  l === M.LHS ? (p = Oe(`M ${u + 10} ${g} c -5 0 -10 5 -10 10 L ${u} ${y - 10} c 0 5 5 10 10 10 M ${u} ${x} h -10`, E), m = He(o, u - 20, x + 6, \"end\", E)) : (p = Oe(`M ${h - 10} ${g} c 5 0 10 5 10 10 L ${h} ${y - 10} c 0 5 -5 10 -10 10 M ${h} ${x} h 10`, E), m = He(o, h + 20, x + 6, \"start\", E));\n  const b = Yn(\"s-\" + n);\n  return b.appendChild(p), b.appendChild(m), b.summaryObj = t, e.summarySvg.appendChild(b), b;\n}, Un = function() {\n  if (!this.currentNodes)\n    return;\n  const e = this.currentNodes, { parent: t, start: n, end: o } = Kn(e), s = { id: V(), parent: t, start: n, end: o, label: \"summary\" }, i = we(this, s);\n  this.summaries.push(s), this.editSummary(i), this.bus.fire(\"operation\", {\n    name: \"createSummary\",\n    obj: s\n  });\n}, Gn = function(e) {\n  const t = V(), n = { ...e, id: t };\n  we(this, n), this.summaries.push(n), this.bus.fire(\"operation\", {\n    name: \"createSummary\",\n    obj: n\n  });\n}, Jn = function(e) {\n  var n;\n  const t = this.summaries.findIndex((o) => o.id === e);\n  t > -1 && (this.summaries.splice(t, 1), (n = document.querySelector(\"#s-\" + e)) == null || n.remove()), this.bus.fire(\"operation\", {\n    name: \"removeSummary\",\n    obj: { id: e }\n  });\n}, Zn = function(e) {\n  const t = e.children[1].getBBox(), n = 6, o = 3, s = document.createElementNS(\"http://www.w3.org/2000/svg\", \"rect\");\n  N(s, {\n    x: t.x - n + \"\",\n    y: t.y - n + \"\",\n    width: t.width + n * 2 + \"\",\n    height: t.height + n * 2 + \"\",\n    rx: o + \"\",\n    stroke: this.theme.cssVar[\"--selected\"] || \"#4dc4ff\",\n    \"stroke-width\": \"2\",\n    fill: \"none\"\n  }), e.appendChild(s), this.currentSummary = e;\n}, Qn = function() {\n  var e, t;\n  (t = (e = this.currentSummary) == null ? void 0 : e.querySelector(\"rect\")) == null || t.remove(), this.currentSummary = null;\n}, eo = function() {\n  this.summarySvg.innerHTML = \"\", this.summaries.forEach((e) => {\n    try {\n      we(this, e);\n    } catch {\n    }\n  }), this.nodes.insertAdjacentElement(\"beforeend\", this.summarySvg);\n}, to = function(e) {\n  if (!e)\n    return;\n  const t = e.childNodes[1];\n  Ie(this, t, e.summaryObj);\n}, no = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({\n  __proto__: null,\n  createSummary: Un,\n  createSummaryFrom: Gn,\n  editSummary: to,\n  removeSummary: Jn,\n  renderSummary: eo,\n  selectSummary: Zn,\n  unselectSummary: Qn\n}, Symbol.toStringTag, { value: \"Module\" })), L = \"http://www.w3.org/2000/svg\";\nfunction oo(e, t) {\n  const n = document.createElementNS(L, \"svg\");\n  return N(n, {\n    version: \"1.1\",\n    xmlns: L,\n    height: e,\n    width: t\n  }), n;\n}\nfunction so(e, t) {\n  return (parseInt(e) - parseInt(t)) / 2;\n}\nfunction io(e, t, n, o) {\n  const s = document.createElementNS(L, \"g\");\n  let i = \"\";\n  return e.text ? i = e.text.textContent : i = e.childNodes[0].textContent, i.split(`\n`).forEach((c, a) => {\n    const d = document.createElementNS(L, \"text\");\n    N(d, {\n      x: n + parseInt(t.paddingLeft) + \"\",\n      y: o + parseInt(t.paddingTop) + so(t.lineHeight, t.fontSize) * (a + 1) + parseFloat(t.fontSize) * (a + 1) + \"\",\n      \"text-anchor\": \"start\",\n      \"font-family\": t.fontFamily,\n      \"font-size\": `${t.fontSize}`,\n      \"font-weight\": `${t.fontWeight}`,\n      fill: `${t.color}`\n    }), d.innerHTML = c, s.appendChild(d);\n  }), s;\n}\nfunction ro(e, t, n, o) {\n  var c;\n  let s = \"\";\n  (c = e.nodeObj) != null && c.dangerouslySetInnerHTML ? s = e.nodeObj.dangerouslySetInnerHTML : e.text ? s = e.text.textContent : s = e.childNodes[0].textContent;\n  const i = document.createElementNS(L, \"foreignObject\");\n  N(i, {\n    x: n + parseInt(t.paddingLeft) + \"\",\n    y: o + parseInt(t.paddingTop) + \"\",\n    width: t.width,\n    height: t.height\n  });\n  const r = document.createElement(\"div\");\n  return N(r, {\n    xmlns: \"http://www.w3.org/1999/xhtml\",\n    style: `font-family: ${t.fontFamily}; font-size: ${t.fontSize}; font-weight: ${t.fontWeight}; color: ${t.color}; white-space: pre-wrap;`\n  }), r.innerHTML = s, i.appendChild(r), i;\n}\nfunction co(e, t) {\n  const n = getComputedStyle(t), { offsetLeft: o, offsetTop: s } = B(e.nodes, t), i = document.createElementNS(L, \"rect\");\n  return N(i, {\n    x: o + \"\",\n    y: s + \"\",\n    rx: n.borderRadius,\n    ry: n.borderRadius,\n    width: n.width,\n    height: n.height,\n    fill: n.backgroundColor,\n    stroke: n.borderColor,\n    \"stroke-width\": n.borderWidth\n  }), i;\n}\nfunction ne(e, t, n = !1) {\n  const o = getComputedStyle(t), { offsetLeft: s, offsetTop: i } = B(e.nodes, t), r = document.createElementNS(L, \"rect\");\n  N(r, {\n    x: s + \"\",\n    y: i + \"\",\n    rx: o.borderRadius,\n    ry: o.borderRadius,\n    width: o.width,\n    height: o.height,\n    fill: o.backgroundColor,\n    stroke: o.borderColor,\n    \"stroke-width\": o.borderWidth\n  });\n  const c = document.createElementNS(L, \"g\");\n  c.appendChild(r);\n  let a;\n  return n ? a = ro(t, o, s, i) : a = io(t, o, s, i), c.appendChild(a), c;\n}\nfunction lo(e, t) {\n  const n = getComputedStyle(t), { offsetLeft: o, offsetTop: s } = B(e.nodes, t), i = document.createElementNS(L, \"a\"), r = document.createElementNS(L, \"text\");\n  return N(r, {\n    x: o + \"\",\n    y: s + parseInt(n.fontSize) + \"\",\n    \"text-anchor\": \"start\",\n    \"font-family\": n.fontFamily,\n    \"font-size\": `${n.fontSize}`,\n    \"font-weight\": `${n.fontWeight}`,\n    fill: `${n.color}`\n  }), r.innerHTML = t.textContent, i.appendChild(r), i.setAttribute(\"href\", t.href), i;\n}\nfunction ao(e, t) {\n  const n = getComputedStyle(t), { offsetLeft: o, offsetTop: s } = B(e.nodes, t), i = document.createElementNS(L, \"image\");\n  return N(i, {\n    x: o + \"\",\n    y: s + \"\",\n    width: n.width + \"\",\n    height: n.height + \"\",\n    href: t.src\n  }), i;\n}\nconst oe = 100, ho = '<?xml version=\"1.0\" standalone=\"no\"?><!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">', uo = (e, t = !1) => {\n  var u, h, v;\n  const n = e.nodes, o = n.offsetHeight + oe * 2, s = n.offsetWidth + oe * 2, i = oo(o + \"px\", s + \"px\"), r = document.createElementNS(L, \"svg\"), c = document.createElementNS(L, \"rect\");\n  N(c, {\n    x: \"0\",\n    y: \"0\",\n    width: `${s}`,\n    height: `${o}`,\n    fill: e.theme.cssVar[\"--bgcolor\"]\n  }), i.appendChild(c), n.querySelectorAll(\".subLines\").forEach((f) => {\n    const p = f.cloneNode(!0), { offsetLeft: m, offsetTop: g } = B(n, f.parentElement);\n    p.setAttribute(\"x\", `${m}`), p.setAttribute(\"y\", `${g}`), r.appendChild(p);\n  });\n  const a = (u = n.querySelector(\".lines\")) == null ? void 0 : u.cloneNode(!0);\n  a && r.appendChild(a);\n  const d = (h = n.querySelector(\".topiclinks\")) == null ? void 0 : h.cloneNode(!0);\n  d && r.appendChild(d);\n  const l = (v = n.querySelector(\".summary\")) == null ? void 0 : v.cloneNode(!0);\n  return l && r.appendChild(l), n.querySelectorAll(\"me-tpc\").forEach((f) => {\n    f.nodeObj.dangerouslySetInnerHTML ? r.appendChild(ne(e, f, !t)) : (r.appendChild(co(e, f)), r.appendChild(ne(e, f.text, !t)));\n  }), n.querySelectorAll(\".tags > span\").forEach((f) => {\n    r.appendChild(ne(e, f));\n  }), n.querySelectorAll(\".icons > span\").forEach((f) => {\n    r.appendChild(ne(e, f));\n  }), n.querySelectorAll(\".hyper-link\").forEach((f) => {\n    r.appendChild(lo(e, f));\n  }), n.querySelectorAll(\"img\").forEach((f) => {\n    r.appendChild(ao(e, f));\n  }), N(r, {\n    x: oe + \"\",\n    y: oe + \"\",\n    overflow: \"visible\"\n  }), i.appendChild(r), i;\n}, fo = (e, t) => (t && e.insertAdjacentHTML(\"afterbegin\", \"<style>\" + t + \"</style>\"), ho + e.outerHTML);\nfunction po(e) {\n  return new Promise((t, n) => {\n    const o = new FileReader();\n    o.onload = (s) => {\n      t(s.target.result);\n    }, o.onerror = (s) => {\n      n(s);\n    }, o.readAsDataURL(e);\n  });\n}\nconst mo = function(e = !1, t) {\n  const n = uo(this, e), o = fo(n, t);\n  return new Blob([o], { type: \"image/svg+xml\" });\n}, go = async function(e = !1, t) {\n  const n = this.exportSvg(e, t), o = await po(n);\n  return new Promise((s, i) => {\n    const r = new Image();\n    r.setAttribute(\"crossOrigin\", \"anonymous\"), r.onload = () => {\n      const c = document.createElement(\"canvas\");\n      c.width = r.width, c.height = r.height, c.getContext(\"2d\").drawImage(r, 0, 0), c.toBlob(s, \"image/png\", 1);\n    }, r.src = o, r.onerror = i;\n  });\n}, vo = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({\n  __proto__: null,\n  exportPng: go,\n  exportSvg: mo\n}, Symbol.toStringTag, { value: \"Module\" }));\nfunction yo(e, t) {\n  return async function(...n) {\n    const o = this.before[t];\n    o && !await o.apply(this, n) || e.apply(this, n);\n  };\n}\nconst Pe = Object.keys(Qe), tt = {};\nfor (let e = 0; e < Pe.length; e++) {\n  const t = Pe[e];\n  tt[t] = yo(Qe[t], t);\n}\nconst bo = {\n  getObjById: ie,\n  generateNewObj: st,\n  layout: ct,\n  linkDiv: gt,\n  editTopic: pt,\n  createWrapper: dt,\n  createParent: ht,\n  createChildren: ut,\n  createTopic: ft,\n  findEle: S,\n  changeTheme: Ft,\n  ...jn,\n  ...tt,\n  ...In,\n  ...no,\n  ...vo,\n  init(e) {\n    if (e = JSON.parse(JSON.stringify(e)), !e || !e.nodeData)\n      return new Error(\"MindElixir: `data` is required\");\n    e.direction !== void 0 && (this.direction = e.direction), this.changeTheme(e.theme || this.theme, !1), this.nodeData = e.nodeData, P(this.nodeData), this.arrows = e.arrows || [], this.summaries = e.summaries || [], this.tidyArrow(), this.toolBar && $t(this), this.keypress && Ct(this, this.keypress), this.editable && zt(this), this.contextMenu && this.disposable.push(vt(this, this.contextMenu)), this.draggable && this.disposable.push(Mt(this)), this.allowUndo && this.disposable.push(kt(this)), this.toCenter(), this.layout(), this.linkDiv();\n  },\n  destroy() {\n    var e;\n    this.disposable.forEach((t) => t()), this.el && (this.el.innerHTML = \"\"), this.el = void 0, this.nodeData = void 0, this.arrows = void 0, this.summaries = void 0, this.currentArrow = void 0, this.currentNodes = void 0, this.currentSummary = void 0, this.waitCopy = void 0, this.theme = void 0, this.direction = void 0, this.bus = void 0, this.container = void 0, this.map = void 0, this.lines = void 0, this.linkController = void 0, this.linkSvgGroup = void 0, this.P2 = void 0, this.P3 = void 0, this.line1 = void 0, this.line2 = void 0, this.nodes = void 0, (e = this.selection) == null || e.destroy(), this.selection = void 0;\n  }\n};\nfunction xo({ pT: e, pL: t, pW: n, pH: o, cT: s, cL: i, cW: r, cH: c, direction: a, containerHeight: d }) {\n  let l = t + n / 2;\n  const u = e + o / 2;\n  let h;\n  a === M.LHS ? h = i + r : h = i;\n  const v = s + c / 2, p = (1 - Math.abs(v - u) / d) * 0.25 * (n / 2);\n  return a === M.LHS ? l = l - n / 10 - p : l = l + n / 10 + p, `M ${l} ${u} Q ${l} ${v} ${h} ${v}`;\n}\nfunction wo({ pT: e, pL: t, pW: n, pH: o, cT: s, cL: i, cW: r, cH: c, direction: a, isFirst: d }) {\n  const l = parseInt(this.container.style.getPropertyValue(\"--gap\"));\n  let u = 0, h = 0;\n  d ? u = e + o / 2 : u = e + o;\n  const v = s + c;\n  let f = 0, p = 0, m = 0;\n  const g = Math.abs(u - v) / 300 * l;\n  return a === M.LHS ? (m = t, f = m + l, p = m - l, h = i + l, `M ${f} ${u} C ${m} ${u} ${m + g} ${v} ${p} ${v} H ${h}`) : (m = t + n, f = m - l, p = m + l, h = i + r - l, `M ${f} ${u} C ${m} ${u} ${m - g} ${v} ${p} ${v} H ${h}`);\n}\nconst Eo = \"5.0.0-beta.21\";\nfunction Co(e) {\n  return {\n    x: 0,\n    y: 0,\n    moved: !1,\n    // diffrentiate click and move\n    mousedown: !1,\n    onMove(t) {\n      if (this.mousedown) {\n        this.moved = !0;\n        const n = t.movementX, o = t.movementY;\n        e.move(n, o);\n      }\n    },\n    clear() {\n      setTimeout(() => {\n        this.moved = !1, this.mousedown = !1, e.map.style.transition = \"transform 0.3s\";\n      }, 0);\n    }\n  };\n}\nconst J = document;\nfunction j({\n  el: e,\n  direction: t,\n  locale: n,\n  draggable: o,\n  editable: s,\n  contextMenu: i,\n  toolBar: r,\n  keypress: c,\n  mouseSelectionButton: a,\n  selectionContainer: d,\n  before: l,\n  newTopicName: u,\n  allowUndo: h,\n  generateMainBranch: v,\n  generateSubBranch: f,\n  overflowHidden: p,\n  theme: m,\n  alignment: g\n}) {\n  let y = null;\n  const x = Object.prototype.toString.call(e);\n  if (x === \"[object HTMLDivElement]\" ? y = e : x === \"[object String]\" && (y = document.querySelector(e)), !y)\n    throw new Error(\"MindElixir: el is not a valid element\");\n  y.style.position = \"relative\", y.innerHTML = \"\", this.el = y, this.disposable = [], this.before = l || {}, this.locale = n || \"en\", this.contextMenu = i === void 0 ? !0 : i, this.toolBar = r === void 0 ? !0 : r, this.keypress = c === void 0 ? !0 : c, this.mouseSelectionButton = a || 0, this.direction = typeof t == \"number\" ? t : 1, this.draggable = o === void 0 ? !0 : o, this.newTopicName = u || \"new node\", this.editable = s === void 0 ? !0 : s, this.allowUndo = h === void 0 ? !1 : h, this.currentNodes = [], this.currentArrow = null, this.scaleVal = 1, this.tempDirection = null, this.generateMainBranch = v || xo, this.generateSubBranch = f || wo, this.overflowHidden = p || !1, this.dragMoveHelper = Co(this), this.bus = rt(), this.container = J.createElement(\"div\"), this.selectionContainer = d || this.container, this.container.className = \"map-container\";\n  const E = window.matchMedia(\"(prefers-color-scheme: dark)\");\n  this.theme = m || (E.matches ? Re : Be);\n  const b = J.createElement(\"div\");\n  b.className = \"map-canvas\", setTimeout(() => {\n    b.style.transition = \"all 0.3s\";\n  }, 300), this.map = b, this.map.setAttribute(\"tabindex\", \"0\"), this.container.appendChild(this.map), this.el.appendChild(this.container), this.nodes = J.createElement(\"me-nodes\"), this.nodes.className = \"main-node-container\", this.lines = Z(\"lines\"), this.summarySvg = Z(\"summary\"), this.linkController = Z(\"linkcontroller\"), this.P2 = J.createElement(\"div\"), this.P3 = J.createElement(\"div\"), this.P2.className = this.P3.className = \"circle\", this.P2.style.display = this.P3.style.display = \"none\", this.line1 = Ce(), this.line2 = Ce(), this.linkController.appendChild(this.line1), this.linkController.appendChild(this.line2), this.linkSvgGroup = Z(\"topiclinks\"), this.alignment = g ?? \"root\", this.map.appendChild(this.nodes), this.overflowHidden ? this.container.style.overflow = \"hidden\" : this.disposable.push(it(this));\n}\nj.prototype = bo;\nObject.defineProperty(j.prototype, \"currentNode\", {\n  get() {\n    return this.currentNodes.at(-1);\n  },\n  enumerable: !0\n});\nj.LEFT = A;\nj.RIGHT = H;\nj.SIDE = ae;\nj.THEME = Be;\nj.DARK_THEME = Re;\nj.version = Eo;\nj.E = S;\nj.new = (e) => ({\n  nodeData: {\n    id: V(),\n    topic: e || \"new topic\",\n    children: []\n  }\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/mind-elixir@5.0.0-beta.21/node_modules/mind-elixir/dist/MindElixir.js\n"));

/***/ })

}]);